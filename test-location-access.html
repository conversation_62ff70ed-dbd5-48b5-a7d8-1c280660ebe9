<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Location Access Control</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Location Access Control Test</h1>
    <p>This page tests the location-based access control for staff members.</p>

    <div class="test-section info">
        <h3>Test Instructions</h3>
        <ol>
            <li>Login as a staff member (role: "staff") on the main application</li>
            <li>Click the buttons below to test location access</li>
            <li>Verify that staff can only see their assigned location</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>Current User Info</h3>
        <button onclick="checkCurrentUser()">Check Current User</button>
        <div id="userInfo"></div>
    </div>

    <div class="test-section">
        <h3>Location Access Test</h3>
        <button onclick="testLocationAccess()">Test Location Access</button>
        <div id="locationTest"></div>
    </div>

    <div class="test-section">
        <h3>Location Selector Test</h3>
        <button onclick="testLocationSelector()">Test Location Selector Logic</button>
        <div id="selectorTest"></div>
    </div>

    <script>
        function checkCurrentUser() {
            const userInfo = document.getElementById('userInfo');
            
            try {
                const storedUser = localStorage.getItem('vanity_user');
                const currentLocation = localStorage.getItem('vanity_location');
                
                if (storedUser) {
                    const user = JSON.parse(storedUser);
                    userInfo.innerHTML = `
                        <h4>Current User:</h4>
                        <pre>${JSON.stringify(user, null, 2)}</pre>
                        <h4>Current Location:</h4>
                        <pre>${currentLocation || 'Not set'}</pre>
                    `;
                } else {
                    userInfo.innerHTML = '<p class="error">No user logged in. Please login first.</p>';
                }
            } catch (error) {
                userInfo.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        function testLocationAccess() {
            const locationTest = document.getElementById('locationTest');
            
            try {
                const storedUser = localStorage.getItem('vanity_user');
                if (!storedUser) {
                    locationTest.innerHTML = '<p class="error">No user logged in. Please login first.</p>';
                    return;
                }
                
                const user = JSON.parse(storedUser);
                const testLocations = ['all', 'loc1', 'loc2', 'loc3', 'home', 'online'];
                
                // Simulate the canAccessLocation function logic
                function canAccessLocation(locationId) {
                    if (!user) return false;

                    // Super admin and org admin can access all locations
                    if (user.role === 'super_admin' || user.role === 'org_admin') {
                        return true;
                    }

                    // Handle "all" location access - only for users with proper permissions
                    if (locationId === 'all') {
                        return user.role === 'super_admin' || 
                               user.role === 'org_admin' || 
                               user.locations.includes('all');
                    }

                    // Online location can be accessed by users who have it in their locations
                    if (locationId === 'online') {
                        return user.locations.includes('online') || user.locations.includes('all');
                    }

                    // If the user has "all" in their locations, they can access all locations
                    if (user.locations.includes('all')) {
                        return true;
                    }

                    // Check if the location is in the user's assigned locations
                    return user.locations.includes(locationId);
                }
                
                let results = '<h4>Location Access Results:</h4><ul>';
                testLocations.forEach(locationId => {
                    const canAccess = canAccessLocation(locationId);
                    const status = canAccess ? '✅ CAN ACCESS' : '❌ CANNOT ACCESS';
                    results += `<li><strong>${locationId}</strong>: ${status}</li>`;
                });
                results += '</ul>';
                
                // Test if "All Locations" should be shown in selector
                const accessibleLocations = testLocations.filter(loc => canAccessLocation(loc));
                const shouldShowAll = canAccessLocation('all') && accessibleLocations.length > 1;
                
                results += `<h4>Location Selector Logic:</h4>`;
                results += `<p><strong>Should show "All Locations":</strong> ${shouldShowAll ? '✅ YES' : '❌ NO'}</p>`;
                results += `<p><strong>Accessible locations count:</strong> ${accessibleLocations.length}</p>`;
                results += `<p><strong>Accessible locations:</strong> ${accessibleLocations.join(', ')}</p>`;
                
                locationTest.innerHTML = results;
            } catch (error) {
                locationTest.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        function testLocationSelector() {
            const selectorTest = document.getElementById('selectorTest');
            
            try {
                const storedUser = localStorage.getItem('vanity_user');
                if (!storedUser) {
                    selectorTest.innerHTML = '<p class="error">No user logged in. Please login first.</p>';
                    return;
                }
                
                const user = JSON.parse(storedUser);
                
                // Test the expected behavior for different user types
                let results = '<h4>Expected Behavior by Role:</h4>';
                
                if (user.role === 'super_admin' || user.role === 'org_admin') {
                    results += '<p class="success">✅ Admin users should see "All Locations" and all individual locations</p>';
                } else if (user.role === 'staff' && user.locations.length === 1 && !user.locations.includes('all')) {
                    results += '<p class="success">✅ Location-restricted staff should NOT see "All Locations"</p>';
                    results += `<p class="success">✅ Should only see: ${user.locations.join(', ')}</p>';
                } else {
                    results += '<p class="info">ℹ️ Multi-location user - should see "All Locations" if they have access to multiple locations</p>';
                }
                
                selectorTest.innerHTML = results;
            } catch (error) {
                selectorTest.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        // Auto-check user on page load
        window.onload = function() {
            checkCurrentUser();
        };
    </script>
</body>
</html>
