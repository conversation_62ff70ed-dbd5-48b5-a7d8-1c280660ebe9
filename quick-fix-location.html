<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Fix Location Access</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Quick Fix: Location Access Control</h1>
    <p>This page provides a quick fix for the location access issue.</p>

    <div class="section error">
        <h3>❌ Current Problem</h3>
        <p>Staff member is seeing all location buttons instead of being restricted to D-Ring Road only.</p>
    </div>

    <div class="section">
        <h3>🔧 Quick Fix</h3>
        <p>Click the button below to set up the correct staff user data:</p>
        <button onclick="quickFix()">Apply Quick Fix</button>
        <div id="fixResults"></div>
    </div>

    <div class="section">
        <h3>✅ Verify Fix</h3>
        <p>After applying the fix:</p>
        <ol>
            <li>Refresh the appointments page</li>
            <li>You should only see "D-ring road" button</li>
            <li>No other location buttons should be visible</li>
        </ol>
        <button onclick="window.open('/dashboard/appointments', '_blank')">Open Appointments Page</button>
    </div>

    <div class="section">
        <h3>🔍 Check Current State</h3>
        <button onclick="checkState()">Check Current State</button>
        <div id="stateResults"></div>
    </div>

    <script>
        function quickFix() {
            const fixResults = document.getElementById('fixResults');
            
            try {
                // Clear all existing data
                localStorage.clear();
                
                // Set up the correct staff user
                const staffUser = {
                    id: "1",
                    name: "Demo Staff",
                    email: "<EMAIL>",
                    role: "staff",
                    locations: ["loc1"] // Only D-Ring Road
                };
                
                // Set user and location data
                localStorage.setItem('vanity_user', JSON.stringify(staffUser));
                localStorage.setItem('vanity_location', 'loc1');
                
                fixResults.innerHTML = `
                    <div class="success">
                        <h4>✅ Quick Fix Applied Successfully!</h4>
                        <p><strong>User:</strong> ${staffUser.name}</p>
                        <p><strong>Role:</strong> ${staffUser.role}</p>
                        <p><strong>Assigned Location:</strong> D-Ring Road (loc1)</p>
                        <p><strong>Current Location:</strong> loc1</p>
                        <br>
                        <p><strong>Next Step:</strong> Refresh the appointments page to see the changes.</p>
                    </div>
                `;
            } catch (error) {
                fixResults.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        function checkState() {
            const stateResults = document.getElementById('stateResults');
            
            try {
                const storedUser = localStorage.getItem('vanity_user');
                const currentLocation = localStorage.getItem('vanity_location');
                
                if (!storedUser) {
                    stateResults.innerHTML = '<p class="error">❌ No user data found</p>';
                    return;
                }
                
                const user = JSON.parse(storedUser);
                
                let results = '<h4>Current State:</h4>';
                results += `<pre>${JSON.stringify(user, null, 2)}</pre>`;
                results += `<p><strong>Current Location:</strong> ${currentLocation}</p>`;
                
                // Check if the configuration is correct
                const isCorrect = user.role === 'staff' && 
                                 user.locations.length === 1 && 
                                 user.locations[0] === 'loc1' && 
                                 currentLocation === 'loc1' &&
                                 !user.locations.includes('all');
                
                if (isCorrect) {
                    results += '<div class="success"><p>✅ Configuration is CORRECT! Staff should only see D-Ring Road.</p></div>';
                } else {
                    results += '<div class="error"><p>❌ Configuration is INCORRECT. Please apply the quick fix.</p></div>';
                }
                
                stateResults.innerHTML = results;
            } catch (error) {
                stateResults.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        // Auto-check state on page load
        window.onload = function() {
            checkState();
        };
    </script>
</body>
</html>
