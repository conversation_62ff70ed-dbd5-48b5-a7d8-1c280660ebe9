/**
 * Setup Mekdes Staff Account with Location-Based Access Control
 * 
 * This script creates a complete user account for <PERSON><PERSON><PERSON> with:
 * 1. Staff record in HR system
 * 2. User account for login
 * 3. Location-based access control (D-Ring Road only)
 * 4. Proper role assignment
 * 5. Custom password setup
 */

// Configuration
const MEKDES_CONFIG = {
  // Staff Details
  name: "<PERSON><PERSON><PERSON>",
  email: "<EMAIL>",
  phone: "+974 1234 5680",
  role: "staff",
  password: "temp123",
  
  // Location Assignment (D-Ring Road only)
  locationId: "cmc4ul8je0001nzzklf81qile", // D-Ring Road location ID
  locationName: "D-ring road",
  
  // Additional Details
  employeeNumber: "VH001",
  status: "Active",
  homeService: false,
  
  // HR Documents
  dateOfBirth: "15-03-90",
  qidNumber: "***********",
  passportNumber: "*********",
  qidValidity: "31-12-25",
  passportValidity: "15-06-30",
  medicalValidity: "20-03-26"
}

/**
 * Step 1: Create or update staff member in HR system
 */
async function createStaffMember() {
  console.log('👤 Step 1: Creating/updating staff member...')
  
  try {
    const staffData = {
      name: MEKDES_CONFIG.name,
      email: MEKDES_CONFIG.email,
      phone: MEKDES_CONFIG.phone,
      role: MEKDES_CONFIG.role,
      locations: [MEKDES_CONFIG.locationId], // Only D-Ring Road
      status: MEKDES_CONFIG.status,
      homeService: MEKDES_CONFIG.homeService,
      employeeNumber: MEKDES_CONFIG.employeeNumber,
      dateOfBirth: MEKDES_CONFIG.dateOfBirth,
      qidNumber: MEKDES_CONFIG.qidNumber,
      passportNumber: MEKDES_CONFIG.passportNumber,
      qidValidity: MEKDES_CONFIG.qidValidity,
      passportValidity: MEKDES_CONFIG.passportValidity,
      medicalValidity: MEKDES_CONFIG.medicalValidity,
      avatar: "M", // First letter of name
      color: "bg-purple-100 text-purple-800"
    }

    console.log('📤 Sending staff creation request...')
    const response = await fetch('/api/staff', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(staffData)
    })

    if (!response.ok) {
      const errorData = await response.text()
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`)
    }

    const result = await response.json()
    console.log('✅ Staff member created successfully')
    console.log(`   - Staff ID: ${result.staff?.id}`)
    console.log(`   - User ID: ${result.user?.id}`)
    console.log(`   - Email: ${result.staff?.email}`)
    console.log(`   - Locations: ${result.staff?.locations?.length} location(s)`)
    
    return result
  } catch (error) {
    console.error('❌ Error creating staff member:', error)
    throw error
  }
}

/**
 * Step 2: Create location-restricted role for staff
 */
async function createLocationRestrictedRole() {
  console.log('\n🔐 Step 2: Creating location-restricted role...')
  
  try {
    // Get current roles from localStorage
    const roles = JSON.parse(localStorage.getItem('admin_roles') || '[]')
    
    // Check if D-Ring Road Staff role already exists
    let dRingStaffRole = roles.find(role => role.name === 'D-Ring Road Staff')
    
    if (!dRingStaffRole) {
      // Create new role with location restrictions
      dRingStaffRole = {
        id: 'dring_staff',
        name: 'D-Ring Road Staff',
        description: 'Staff member with access only to D-Ring Road location',
        permissions: [
          'view_appointments',
          'manage_appointments',
          'view_clients',
          'view_services',
          'view_pos',
          'process_payments'
        ],
        locationAccess: {
          type: 'specific',
          locations: [MEKDES_CONFIG.locationId] // Only D-Ring Road
        },
        isActive: true,
        createdAt: new Date().toISOString()
      }
      
      roles.push(dRingStaffRole)
      localStorage.setItem('admin_roles', JSON.stringify(roles))
      
      console.log('✅ Created D-Ring Road Staff role')
    } else {
      console.log('✅ D-Ring Road Staff role already exists')
    }
    
    console.log(`   - Role ID: ${dRingStaffRole.id}`)
    console.log(`   - Location Access: ${dRingStaffRole.locationAccess?.type}`)
    console.log(`   - Allowed Locations: ${dRingStaffRole.locationAccess?.locations?.join(', ')}`)
    
    return dRingStaffRole
  } catch (error) {
    console.error('❌ Error creating role:', error)
    throw error
  }
}

/**
 * Step 3: Update user account with custom password and location restrictions
 */
async function updateUserAccount(staffResult) {
  console.log('\n🔑 Step 3: Updating user account...')
  
  try {
    if (!staffResult.user?.id) {
      throw new Error('No user ID found in staff creation result')
    }
    
    // Update user in localStorage (for demo purposes)
    const users = JSON.parse(localStorage.getItem('admin_users') || '[]')
    
    // Find or create user
    let userIndex = users.findIndex(u => u.email === MEKDES_CONFIG.email)
    
    const userData = {
      id: staffResult.user.id,
      name: MEKDES_CONFIG.name,
      email: MEKDES_CONFIG.email,
      role: 'staff', // Base role
      locations: [MEKDES_CONFIG.locationId], // Only D-Ring Road
      status: 'Active',
      avatar: 'M',
      color: 'bg-purple-100 text-purple-800',
      lastLogin: 'Never',
      password: MEKDES_CONFIG.password, // Custom password
      locationRestricted: true, // Flag for location restrictions
      assignedLocation: MEKDES_CONFIG.locationName
    }
    
    if (userIndex === -1) {
      users.push(userData)
    } else {
      users[userIndex] = userData
    }
    
    localStorage.setItem('admin_users', JSON.stringify(users))
    
    console.log('✅ User account updated successfully')
    console.log(`   - Email: ${userData.email}`)
    console.log(`   - Password: ${userData.password}`)
    console.log(`   - Role: ${userData.role}`)
    console.log(`   - Locations: ${userData.locations.join(', ')}`)
    console.log(`   - Location Restricted: ${userData.locationRestricted}`)
    
    return userData
  } catch (error) {
    console.error('❌ Error updating user account:', error)
    throw error
  }
}

/**
 * Step 4: Verify location-based access control
 */
async function verifyLocationAccess() {
  console.log('\n🔍 Step 4: Verifying location-based access control...')
  
  try {
    // Check if user can only access D-Ring Road
    const users = JSON.parse(localStorage.getItem('admin_users') || '[]')
    const mekdesUser = users.find(u => u.email === MEKDES_CONFIG.email)
    
    if (!mekdesUser) {
      throw new Error('Mekdes user not found')
    }
    
    // Verify location restrictions
    const hasOnlyDRingAccess = mekdesUser.locations.length === 1 && 
                               mekdesUser.locations[0] === MEKDES_CONFIG.locationId
    
    if (hasOnlyDRingAccess) {
      console.log('✅ Location access verification PASSED')
      console.log(`   - User has access to: ${mekdesUser.locations.length} location(s)`)
      console.log(`   - Location ID: ${mekdesUser.locations[0]}`)
      console.log(`   - Location Name: ${MEKDES_CONFIG.locationName}`)
      return true
    } else {
      console.log('❌ Location access verification FAILED')
      console.log(`   - User has access to: ${mekdesUser.locations.length} location(s)`)
      console.log(`   - Locations: ${mekdesUser.locations.join(', ')}`)
      return false
    }
  } catch (error) {
    console.error('❌ Error verifying location access:', error)
    return false
  }
}

/**
 * Step 5: Test login functionality
 */
async function testLogin() {
  console.log('\n🧪 Step 5: Testing login functionality...')
  
  try {
    // Simulate login test
    const users = JSON.parse(localStorage.getItem('admin_users') || '[]')
    const mekdesUser = users.find(u => u.email === MEKDES_CONFIG.email)
    
    if (!mekdesUser) {
      throw new Error('User not found for login test')
    }
    
    // Verify credentials
    const credentialsValid = mekdesUser.email === MEKDES_CONFIG.email && 
                            mekdesUser.password === MEKDES_CONFIG.password
    
    if (credentialsValid) {
      console.log('✅ Login test PASSED')
      console.log(`   - Email: ${mekdesUser.email}`)
      console.log(`   - Password: ${mekdesUser.password}`)
      console.log(`   - Role: ${mekdesUser.role}`)
      console.log(`   - Default Location: ${MEKDES_CONFIG.locationName}`)
      return true
    } else {
      console.log('❌ Login test FAILED - Invalid credentials')
      return false
    }
  } catch (error) {
    console.error('❌ Error testing login:', error)
    return false
  }
}

/**
 * Main execution function
 */
async function setupMekdesAccount() {
  console.log('🚀 Setting up Mekdes staff account with location-based access control...')
  console.log('=' .repeat(80))
  
  try {
    // Step 1: Create staff member
    const staffResult = await createStaffMember()
    
    // Step 2: Create location-restricted role
    await createLocationRestrictedRole()
    
    // Step 3: Update user account
    await updateUserAccount(staffResult)
    
    // Step 4: Verify location access
    const accessVerified = await verifyLocationAccess()
    
    // Step 5: Test login
    const loginTested = await testLogin()
    
    // Final summary
    console.log('\n' + '=' .repeat(80))
    console.log('🎉 SETUP COMPLETE!')
    console.log('=' .repeat(80))
    
    if (accessVerified && loginTested) {
      console.log('✅ All tests PASSED - Mekdes account is ready!')
      console.log('\n📋 Login Details:')
      console.log(`   - Email: ${MEKDES_CONFIG.email}`)
      console.log(`   - Password: ${MEKDES_CONFIG.password}`)
      console.log(`   - Role: ${MEKDES_CONFIG.role}`)
      console.log(`   - Assigned Location: ${MEKDES_CONFIG.locationName}`)
      console.log(`   - Location Access: Restricted to D-Ring Road only`)
      
      console.log('\n🔒 Security Features:')
      console.log('   - ✅ Location-based access control enabled')
      console.log('   - ✅ Cannot switch to other locations')
      console.log('   - ✅ Only sees D-Ring Road appointments')
      console.log('   - ✅ Cannot access other location data')
      
      return true
    } else {
      console.log('❌ Some tests FAILED - Please check the setup')
      return false
    }
    
  } catch (error) {
    console.error('💥 Setup failed:', error)
    return false
  }
}

// Export for use in browser console or as module
if (typeof window !== 'undefined') {
  window.setupMekdesAccount = setupMekdesAccount
  console.log('🔧 Run setupMekdesAccount() in the browser console to execute')
} else {
  module.exports = { setupMekdesAccount, MEKDES_CONFIG }
}
