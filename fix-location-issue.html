<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Location Issue</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Fix Location Access Issue</h1>
    <p>This page helps diagnose and fix the location access control issue.</p>

    <div class="section info">
        <h3>Current Issue</h3>
        <p>Staff member is assigned to D-Ring Road (loc1) but the location selector shows Muaither (loc2) and displays all staff members.</p>
    </div>

    <div class="section">
        <h3>Current State</h3>
        <button onclick="checkCurrentState()">Check Current State</button>
        <div id="currentState"></div>
    </div>

    <div class="section">
        <h3>Fix Actions</h3>
        <button onclick="clearLocationData()">Clear Location Data</button>
        <button onclick="forceCorrectLocation()">Force Correct Location</button>
        <button onclick="resetUserData()">Reset User Data</button>
        <div id="fixResults"></div>
    </div>

    <div class="section">
        <h3>Test Login</h3>
        <button onclick="testStaffLogin()">Test Staff Login (D-Ring Road)</button>
        <div id="loginResults"></div>
    </div>

    <script>
        function checkCurrentState() {
            const currentState = document.getElementById('currentState');
            
            try {
                const storedUser = localStorage.getItem('vanity_user');
                const currentLocation = localStorage.getItem('vanity_location');
                
                let results = '<h4>Current State:</h4>';
                
                if (storedUser) {
                    const user = JSON.parse(storedUser);
                    results += `<pre>User: ${JSON.stringify(user, null, 2)}</pre>`;
                    results += `<p><strong>Current Location:</strong> ${currentLocation || 'Not set'}</p>`;
                    
                    // Check if there's a mismatch
                    if (user.role === 'staff' && user.locations.includes('loc1') && currentLocation !== 'loc1') {
                        results += '<p class="error">❌ ISSUE DETECTED: Staff assigned to loc1 but current location is ' + currentLocation + '</p>';
                    } else if (user.role === 'staff' && user.locations.includes('loc1') && currentLocation === 'loc1') {
                        results += '<p class="success">✅ CORRECT: Staff assigned to loc1 and current location is loc1</p>';
                    }
                } else {
                    results += '<p class="error">No user logged in</p>';
                }
                
                currentState.innerHTML = results;
            } catch (error) {
                currentState.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        function clearLocationData() {
            const fixResults = document.getElementById('fixResults');
            
            try {
                localStorage.removeItem('vanity_location');
                fixResults.innerHTML = '<p class="success">✅ Cleared location data from localStorage</p>';
            } catch (error) {
                fixResults.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        function forceCorrectLocation() {
            const fixResults = document.getElementById('fixResults');
            
            try {
                const storedUser = localStorage.getItem('vanity_user');
                if (!storedUser) {
                    fixResults.innerHTML = '<p class="error">No user logged in</p>';
                    return;
                }
                
                const user = JSON.parse(storedUser);
                
                if (user.role === 'staff' && user.locations.includes('loc1')) {
                    localStorage.setItem('vanity_location', 'loc1');
                    fixResults.innerHTML = '<p class="success">✅ Forced location to loc1 (D-Ring Road)</p>';
                } else {
                    fixResults.innerHTML = '<p class="info">User is not a staff member assigned to loc1</p>';
                }
            } catch (error) {
                fixResults.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        function resetUserData() {
            const fixResults = document.getElementById('fixResults');
            
            try {
                localStorage.removeItem('vanity_user');
                localStorage.removeItem('vanity_location');
                fixResults.innerHTML = '<p class="success">✅ Reset all user data. Please login again.</p>';
            } catch (error) {
                fixResults.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        function testStaffLogin() {
            const loginResults = document.getElementById('loginResults');
            
            try {
                // Clear existing data
                localStorage.removeItem('vanity_user');
                localStorage.removeItem('vanity_location');
                
                // Create correct staff user data
                const staffUser = {
                    id: "1",
                    name: "Demo Staff",
                    email: "<EMAIL>",
                    role: "staff",
                    locations: ["loc1"] // D-Ring Road only
                };
                
                // Set user data
                localStorage.setItem('vanity_user', JSON.stringify(staffUser));
                localStorage.setItem('vanity_location', 'loc1');
                
                loginResults.innerHTML = `
                    <p class="success">✅ Test staff login completed</p>
                    <p><strong>User:</strong> ${staffUser.name}</p>
                    <p><strong>Role:</strong> ${staffUser.role}</p>
                    <p><strong>Locations:</strong> ${staffUser.locations.join(', ')}</p>
                    <p><strong>Current Location:</strong> loc1</p>
                    <p class="info">Please refresh the page to see the changes take effect.</p>
                `;
            } catch (error) {
                loginResults.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        // Auto-check state on page load
        window.onload = function() {
            checkCurrentState();
        };
    </script>
</body>
</html>
