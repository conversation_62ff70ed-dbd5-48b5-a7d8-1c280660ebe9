import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 Configuring location-based access control for Mek<PERSON>be...')

    // Step 1: Create D-Ring Road Staff Role (using localStorage simulation)
    console.log('\n📋 Step 1: Creating D-Ring Road Staff Role...')

    const dRingRoadStaffRole = {
      id: 'd-ring-road-staff',
      name: 'D-Ring Road Staff',
      description: 'Staff members with access restricted to D-Ring Road location only',
      userCount: 1,
      permissions: [
        'view_appointments',
        'manage_appointments',
        'view_clients',
        'manage_clients',
        'view_services',
        'view_inventory',
        'process_sales',
        'view_reports'
      ],
      locationAccess: {
        type: 'specific',
        locations: ['loc1'] // D-Ring Road location ID
      }
    }

    console.log('✅ D-Ring Road Staff role configured successfully')

    // Step 2: Find Mekdes <PERSON> in the database
    console.log('\n👤 Step 2: Finding Mekdes <PERSON> in the database...')

    let mekdesUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        staffProfile: {
          include: {
            locations: {
              include: {
                location: true
              }
            }
          }
        }
      }
    })

    if (!mekdesUser) {
      console.log('❌ Mekdes Abebe not found with primary email')

      const allStaff = await prisma.staffMember.findMany({
        include: {
          user: true,
          locations: {
            include: {
              location: true
            }
          }
        }
      })

      const staffList = allStaff.map(staff => ({
        name: staff.name,
        email: staff.user.email,
        role: staff.user.role,
        jobRole: staff.jobRole
      }))

      return NextResponse.json({
        success: false,
        message: 'Mekdes Abebe not found in database',
        availableStaff: staffList
      })
    }

    console.log('✅ Found Mekdes Abebe in database')
    console.log(`   - Name: ${mekdesUser.staffProfile?.name}`)
    console.log(`   - Email: ${mekdesUser.email}`)
    console.log(`   - Current Role: ${mekdesUser.role}`)

    // Step 3: Update Mekdes Abebe's role assignment
    console.log('\n🔄 Step 3: Updating Mekdes Abebe\'s role assignment...')

    // Update user role in database to STAFF (since D-Ring Road Staff maps to STAFF)
    await prisma.user.update({
      where: { id: mekdesUser.id },
      data: {
        role: 'STAFF' // Database role
      }
    })

    // Update staff member's job role to the new role name
    if (mekdesUser.staffProfile) {
      await prisma.staffMember.update({
        where: { id: mekdesUser.staffProfile.id },
        data: {
          jobRole: 'D-Ring Road Staff' // Specific job role for display
        }
      })

      // Get the actual D-Ring Road location ID from database
      const dRingLocation = await prisma.location.findFirst({
        where: { name: 'D-ring road' }
      })

      if (dRingLocation) {
        // Remove existing location assignments
        await prisma.staffLocation.deleteMany({
          where: { staffId: mekdesUser.staffProfile.id }
        })

        // Add D-Ring Road location assignment
        await prisma.staffLocation.create({
          data: {
            staffId: mekdesUser.staffProfile.id,
            locationId: dRingLocation.id
          }
        })

        console.log(`✅ Assigned to D-Ring Road location: ${dRingLocation.id}`)
      }
    }

    console.log('✅ Updated Mekdes Abebe\'s role assignment')

    return NextResponse.json({
      success: true,
      message: 'Location-based access control configured successfully for Mekdes Abebe',
      details: {
        roleCreated: dRingRoadStaffRole.name,
        userUpdated: mekdesUser.staffProfile?.name,
        email: mekdesUser.email,
        locationAccess: 'D-Ring Road only',
        permissions: dRingRoadStaffRole.permissions,
        roleConfiguration: dRingRoadStaffRole
      }
    })

  } catch (error) {
    console.error('❌ Error configuring location-based access control:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to configure location-based access control',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
