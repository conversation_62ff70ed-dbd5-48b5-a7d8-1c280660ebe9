import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

/**
 * API endpoint to set up Mekdes staff account with location-based access control
 * 
 * POST /api/setup-mekdes
 * 
 * This endpoint:
 * 1. Creates/updates Mekdes staff member
 * 2. <PERSON><PERSON>s user account with custom password
 * 3. Assigns only D-Ring Road location access
 * 4. Sets up proper role and permissions
 */

const MEKDES_CONFIG = {
  name: "Mek<PERSON>",
  email: "<EMAIL>",
  phone: "+974 1234 5680",
  password: "temp123",
  role: "staff",
  employeeNumber: "VH001",
  dateOfBirth: "1990-03-15", // ISO format for database
  qidNumber: "***********",
  passportNumber: "*********",
  qidValidity: "2025-12-31",
  passportValidity: "2030-06-15",
  medicalValidity: "2026-03-20"
}

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Setting up Mek<PERSON> staff account...')

    // Step 1: Find D-Ring Road location
    const dRingLocation = await prisma.location.findFirst({
      where: { name: 'D-ring road' }
    })

    if (!dRingLocation) {
      return NextResponse.json(
        { error: 'D-Ring Road location not found' },
        { status: 404 }
      )
    }

    console.log(`✅ Found D-Ring Road location: ${dRingLocation.id}`)

    // Step 2: Hash the password
    const hashedPassword = await bcrypt.hash(MEKDES_CONFIG.password, 10)

    // Step 3: Create or update user account
    let user = await prisma.user.findUnique({
      where: { email: MEKDES_CONFIG.email }
    })

    if (user) {
      // Update existing user
      user = await prisma.user.update({
        where: { id: user.id },
        data: {
          password: hashedPassword,
          role: 'STAFF',
          isActive: true
        }
      })
      console.log(`✅ Updated existing user: ${user.id}`)
    } else {
      // Create new user
      user = await prisma.user.create({
        data: {
          email: MEKDES_CONFIG.email,
          password: hashedPassword,
          role: 'STAFF',
          isActive: true
        }
      })
      console.log(`✅ Created new user: ${user.id}`)
    }

    // Step 4: Create or update staff member
    let staffMember = await prisma.staffMember.findUnique({
      where: { userId: user.id },
      include: {
        locations: {
          include: {
            location: true
          }
        }
      }
    })

    if (staffMember) {
      // Update existing staff member
      staffMember = await prisma.staffMember.update({
        where: { id: staffMember.id },
        data: {
          name: MEKDES_CONFIG.name,
          phone: MEKDES_CONFIG.phone,
          jobRole: MEKDES_CONFIG.role,
          status: 'ACTIVE',
          employeeNumber: MEKDES_CONFIG.employeeNumber,
          dateOfBirth: new Date(MEKDES_CONFIG.dateOfBirth),
          qidNumber: MEKDES_CONFIG.qidNumber,
          passportNumber: MEKDES_CONFIG.passportNumber,
          qidValidity: MEKDES_CONFIG.qidValidity,
          passportValidity: MEKDES_CONFIG.passportValidity,
          medicalValidity: MEKDES_CONFIG.medicalValidity,
          avatar: 'M',
          color: 'bg-purple-100 text-purple-800'
        },
        include: {
          locations: {
            include: {
              location: true
            }
          }
        }
      })
      console.log(`✅ Updated existing staff member: ${staffMember.id}`)
    } else {
      // Create new staff member
      staffMember = await prisma.staffMember.create({
        data: {
          userId: user.id,
          name: MEKDES_CONFIG.name,
          phone: MEKDES_CONFIG.phone,
          jobRole: MEKDES_CONFIG.role,
          status: 'ACTIVE',
          employeeNumber: MEKDES_CONFIG.employeeNumber,
          dateOfBirth: new Date(MEKDES_CONFIG.dateOfBirth),
          qidNumber: MEKDES_CONFIG.qidNumber,
          passportNumber: MEKDES_CONFIG.passportNumber,
          qidValidity: MEKDES_CONFIG.qidValidity,
          passportValidity: MEKDES_CONFIG.passportValidity,
          medicalValidity: MEKDES_CONFIG.medicalValidity,
          avatar: 'M',
          color: 'bg-purple-100 text-purple-800'
        },
        include: {
          locations: {
            include: {
              location: true
            }
          }
        }
      })
      console.log(`✅ Created new staff member: ${staffMember.id}`)
    }

    // Step 5: Clear existing location assignments
    await prisma.staffLocation.deleteMany({
      where: { staffId: staffMember.id }
    })

    // Step 6: Assign only D-Ring Road location
    await prisma.staffLocation.create({
      data: {
        staffId: staffMember.id,
        locationId: dRingLocation.id
      }
    })

    console.log(`✅ Assigned staff to D-Ring Road location only`)

    // Step 7: Verify the setup
    const verificationStaff = await prisma.staffMember.findUnique({
      where: { id: staffMember.id },
      include: {
        user: true,
        locations: {
          include: {
            location: true
          }
        }
      }
    })

    const locationNames = verificationStaff?.locations.map(sl => sl.location.name) || []
    const hasOnlyDRingAccess = locationNames.length === 1 && locationNames[0] === 'D-ring road'

    if (!hasOnlyDRingAccess) {
      console.error('❌ Location access verification failed')
      return NextResponse.json(
        { error: 'Location access setup failed' },
        { status: 500 }
      )
    }

    console.log('✅ Location access verification passed')

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Mekdes staff account setup completed successfully',
      data: {
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          isActive: user.isActive
        },
        staff: {
          id: staffMember.id,
          name: staffMember.name,
          phone: staffMember.phone,
          jobRole: staffMember.jobRole,
          status: staffMember.status,
          employeeNumber: staffMember.employeeNumber
        },
        locationAccess: {
          locations: locationNames,
          restrictedToSingleLocation: hasOnlyDRingAccess,
          assignedLocation: 'D-ring road'
        },
        loginCredentials: {
          email: MEKDES_CONFIG.email,
          password: MEKDES_CONFIG.password,
          note: 'Password should be changed on first login'
        }
      }
    })

  } catch (error) {
    console.error('💥 Error setting up Mekdes account:', error)
    return NextResponse.json(
      { 
        error: 'Failed to setup Mekdes account',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Verify current setup
    const user = await prisma.user.findUnique({
      where: { email: MEKDES_CONFIG.email },
      include: {
        staffProfile: {
          include: {
            locations: {
              include: {
                location: true
              }
            }
          }
        }
      }
    })

    if (!user || !user.staffProfile) {
      return NextResponse.json({
        exists: false,
        message: 'Mekdes account not found'
      })
    }

    const locationNames = user.staffProfile.locations.map(sl => sl.location.name)
    const hasOnlyDRingAccess = locationNames.length === 1 && locationNames[0] === 'D-ring road'

    return NextResponse.json({
      exists: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        isActive: user.isActive
      },
      staff: {
        id: user.staffProfile.id,
        name: user.staffProfile.name,
        jobRole: user.staffProfile.jobRole,
        status: user.staffProfile.status,
        employeeNumber: user.staffProfile.employeeNumber
      },
      locationAccess: {
        locations: locationNames,
        restrictedToSingleLocation: hasOnlyDRingAccess,
        isCorrectlyConfigured: hasOnlyDRingAccess
      }
    })

  } catch (error) {
    console.error('Error checking Mekdes account:', error)
    return NextResponse.json(
      { error: 'Failed to check account status' },
      { status: 500 }
    )
  }
}
