<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Location Access</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .step {
            background-color: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Debug Location Access Control</h1>
    <p>This page helps debug and fix the location access control issue for staff members.</p>

    <div class="section warning">
        <h3>⚠️ Current Issue</h3>
        <p>Staff member assigned to D-Ring Road (loc1) is seeing Muaither location and all staff members instead of being restricted to their assigned location.</p>
    </div>

    <div class="step">
        <h3>Step 1: Check Current State</h3>
        <button onclick="checkCurrentState()">Check Current State</button>
        <div id="currentState"></div>
    </div>

    <div class="step">
        <h3>Step 2: Fix User Data</h3>
        <button onclick="fixUserData()">Fix Staff User Data</button>
        <div id="fixResults"></div>
    </div>

    <div class="step">
        <h3>Step 3: Test Location Access</h3>
        <button onclick="testLocationAccess()">Test Location Access Logic</button>
        <div id="accessResults"></div>
    </div>

    <div class="step">
        <h3>Step 4: Verify Fix</h3>
        <p>After fixing the data, refresh the appointments page and verify:</p>
        <ul>
            <li>Location selector shows only "D-ring road" (no "All Locations")</li>
            <li>Calendar shows only staff assigned to D-Ring Road</li>
            <li>Cannot switch to other locations</li>
        </ul>
        <button onclick="window.open('/dashboard/appointments', '_blank')">Open Appointments Page</button>
    </div>

    <script>
        function checkCurrentState() {
            const currentState = document.getElementById('currentState');
            
            try {
                const storedUser = localStorage.getItem('vanity_user');
                const currentLocation = localStorage.getItem('vanity_location');
                
                let results = '<h4>Current State Analysis:</h4>';
                
                if (storedUser) {
                    const user = JSON.parse(storedUser);
                    results += `<pre>User Data: ${JSON.stringify(user, null, 2)}</pre>`;
                    results += `<p><strong>Current Location:</strong> ${currentLocation || 'Not set'}</p>`;
                    
                    // Analyze the issue
                    results += '<h4>Issue Analysis:</h4><ul>';
                    
                    if (user.role !== 'staff') {
                        results += `<li class="error">❌ User role is "${user.role}", should be "staff"</li>`;
                    } else {
                        results += '<li class="success">✅ User role is "staff"</li>';
                    }
                    
                    if (!user.locations || !user.locations.includes('loc1')) {
                        results += `<li class="error">❌ User locations are ${JSON.stringify(user.locations)}, should include "loc1"</li>`;
                    } else {
                        results += '<li class="success">✅ User has "loc1" in locations</li>';
                    }
                    
                    if (currentLocation !== 'loc1') {
                        results += `<li class="error">❌ Current location is "${currentLocation}", should be "loc1"</li>`;
                    } else {
                        results += '<li class="success">✅ Current location is "loc1"</li>';
                    }
                    
                    if (user.locations && user.locations.includes('all')) {
                        results += '<li class="error">❌ User has "all" in locations (should not for restricted staff)</li>';
                    } else {
                        results += '<li class="success">✅ User does not have "all" access</li>';
                    }
                    
                    results += '</ul>';
                } else {
                    results += '<p class="error">❌ No user logged in</p>';
                }
                
                currentState.innerHTML = results;
            } catch (error) {
                currentState.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        function fixUserData() {
            const fixResults = document.getElementById('fixResults');
            
            try {
                // Create the correct staff user data
                const correctStaffUser = {
                    id: "1",
                    name: "Demo Staff",
                    email: "<EMAIL>",
                    role: "staff",
                    locations: ["loc1"] // D-Ring Road only
                };
                
                // Clear any existing data
                localStorage.removeItem('vanity_user');
                localStorage.removeItem('vanity_location');
                
                // Set the correct data
                localStorage.setItem('vanity_user', JSON.stringify(correctStaffUser));
                localStorage.setItem('vanity_location', 'loc1');
                
                fixResults.innerHTML = `
                    <div class="success">
                        <h4>✅ Fixed User Data</h4>
                        <p><strong>User:</strong> ${correctStaffUser.name}</p>
                        <p><strong>Role:</strong> ${correctStaffUser.role}</p>
                        <p><strong>Locations:</strong> ${correctStaffUser.locations.join(', ')}</p>
                        <p><strong>Current Location:</strong> loc1</p>
                        <p class="info"><strong>Next:</strong> Refresh the appointments page to see the changes.</p>
                    </div>
                `;
            } catch (error) {
                fixResults.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        function testLocationAccess() {
            const accessResults = document.getElementById('accessResults');
            
            try {
                const storedUser = localStorage.getItem('vanity_user');
                if (!storedUser) {
                    accessResults.innerHTML = '<p class="error">❌ No user data found. Please fix user data first.</p>';
                    return;
                }
                
                const user = JSON.parse(storedUser);
                
                // Test the canAccessLocation function logic
                function canAccessLocation(locationId) {
                    if (!user) return false;

                    // Super admin and org admin can access all locations
                    if (user.role === 'super_admin' || user.role === 'org_admin') {
                        return true;
                    }

                    // Handle "all" location access - only for users with proper permissions
                    if (locationId === 'all') {
                        return user.role === 'super_admin' || 
                               user.role === 'org_admin' || 
                               user.locations.includes('all');
                    }

                    // Online location can be accessed by users who have it in their locations
                    if (locationId === 'online') {
                        return user.locations.includes('online') || user.locations.includes('all');
                    }

                    // If the user has "all" in their locations, they can access all locations
                    if (user.locations.includes('all')) {
                        return true;
                    }

                    // Check if the location is in the user's assigned locations
                    return user.locations.includes(locationId);
                }
                
                function getUserAccessibleLocations() {
                    if (!user) return [];

                    // Super admin and org admin can access all locations
                    if (user.role === 'super_admin' || user.role === 'org_admin') {
                        return ['all'];
                    }

                    // Fallback to user's individual location assignments
                    return user.locations;
                }
                
                const testLocations = ['all', 'loc1', 'loc2', 'loc3', 'home', 'online'];
                const accessibleLocations = getUserAccessibleLocations();
                
                let results = '<h4>Location Access Test Results:</h4><ul>';
                testLocations.forEach(locationId => {
                    const canAccess = canAccessLocation(locationId);
                    const expected = locationId === 'loc1' ? 'SHOULD ACCESS' : 'SHOULD NOT ACCESS';
                    const status = canAccess ? '✅ CAN ACCESS' : '❌ CANNOT ACCESS';
                    const correct = (locationId === 'loc1' && canAccess) || (locationId !== 'loc1' && !canAccess);
                    results += `<li><strong>${locationId}</strong>: ${status} (${expected}) ${correct ? '✅' : '❌'}</li>`;
                });
                results += '</ul>';
                
                // Test location selector logic
                const shouldShowAll = canAccessLocation('all') && accessibleLocations.length > 1;
                
                results += `<h4>Location Selector Logic:</h4>`;
                results += `<p><strong>Accessible locations:</strong> ${accessibleLocations.join(', ')}</p>`;
                results += `<p><strong>Should show "All Locations":</strong> ${shouldShowAll ? '❌ YES (WRONG)' : '✅ NO (CORRECT)'}</p>`;
                
                if (!shouldShowAll && accessibleLocations.length === 1 && accessibleLocations[0] === 'loc1') {
                    results += '<div class="success"><p>✅ PERFECT: Staff will only see D-Ring Road location!</p></div>';
                } else {
                    results += '<div class="error"><p>❌ ISSUE: Staff will still see multiple locations or "All Locations"</p></div>';
                }
                
                accessResults.innerHTML = results;
            } catch (error) {
                accessResults.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        // Auto-check state on page load
        window.onload = function() {
            checkCurrentState();
        };
    </script>
</body>
</html>
