/**
 * Test Mekdes Location Assignment and Calendar Filtering
 * 
 * This script tests that <PERSON><PERSON><PERSON> is properly restricted to D-Ring Road location
 * and that the calendar only shows D-Ring Road staff members.
 */

const MEKDES_CONFIG = {
  email: "<EMAIL>",
  password: "temp123",
  expectedLocationId: "cmc4ul8je0001nzzklf81qile", // D-Ring Road
  expectedLocationName: "D-ring road"
}

/**
 * Test 1: Verify Mekdes account and location assignment
 */
async function testMekdesAccount() {
  console.log('🔍 Test 1: Checking Mekdes account and location assignment...')
  
  try {
    const response = await fetch('/api/setup-mekdes')
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    
    if (data.exists && data.locationAccess.isCorrectlyConfigured) {
      console.log('✅ Mekdes account is correctly configured')
      console.log(`   - Email: ${data.user.email}`)
      console.log(`   - Role: ${data.user.role}`)
      console.log(`   - Assigned Locations: ${data.locationAccess.locations.join(', ')}`)
      console.log(`   - Single Location Access: ${data.locationAccess.restrictedToSingleLocation}`)
      return true
    } else {
      console.log('❌ Mekdes account is not correctly configured')
      return false
    }
  } catch (error) {
    console.error('❌ Error checking Mekdes account:', error)
    return false
  }
}

/**
 * Test 2: Simulate Mekdes login and check location setting
 */
async function testMekdesLogin() {
  console.log('\n🔐 Test 2: Simulating Mekdes login...')
  
  try {
    // Simulate Mekdes login by setting user data
    const mekdesUser = {
      id: "cmc4uojrs0106nzzklazog1fq",
      name: "Mekdes",
      email: MEKDES_CONFIG.email,
      role: "staff",
      locations: [MEKDES_CONFIG.expectedLocationId] // Only D-Ring Road
    }
    
    // Store user data (simulating successful login)
    localStorage.setItem("vanity_user", JSON.stringify(mekdesUser))
    localStorage.setItem("vanity_location", MEKDES_CONFIG.expectedLocationId)
    
    console.log('✅ Mekdes login simulated')
    console.log(`   - User ID: ${mekdesUser.id}`)
    console.log(`   - Name: ${mekdesUser.name}`)
    console.log(`   - Role: ${mekdesUser.role}`)
    console.log(`   - Assigned Locations: ${mekdesUser.locations.join(', ')}`)
    console.log(`   - Current Location: ${MEKDES_CONFIG.expectedLocationId}`)
    
    return true
  } catch (error) {
    console.error('❌ Error simulating Mekdes login:', error)
    return false
  }
}

/**
 * Test 3: Check staff filtering for D-Ring Road location
 */
async function testStaffFiltering() {
  console.log('\n👥 Test 3: Testing staff filtering for D-Ring Road...')
  
  try {
    // Get all staff from API
    const response = await fetch('/api/staff')
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    const allStaff = data.staff || []
    
    console.log(`   - Total staff in system: ${allStaff.length}`)
    
    // Filter staff for D-Ring Road location
    const dRingStaff = allStaff.filter(staff => 
      staff.locations && staff.locations.includes(MEKDES_CONFIG.expectedLocationId)
    )
    
    console.log(`   - Staff assigned to D-Ring Road: ${dRingStaff.length}`)
    console.log(`   - D-Ring Road staff names: ${dRingStaff.map(s => s.name).join(', ')}`)
    
    // Check if Mekdes is in the D-Ring Road staff list
    const mekdesInList = dRingStaff.find(staff => staff.name === "Mekdes")
    if (mekdesInList) {
      console.log('✅ Mekdes is correctly assigned to D-Ring Road staff')
    } else {
      console.log('❌ Mekdes is NOT found in D-Ring Road staff list')
    }
    
    // Staff from other locations should not be visible
    const otherLocationStaff = allStaff.filter(staff => 
      staff.locations && !staff.locations.includes(MEKDES_CONFIG.expectedLocationId)
    )
    
    console.log(`   - Staff from other locations: ${otherLocationStaff.length}`)
    console.log(`   - Other location staff: ${otherLocationStaff.map(s => s.name).join(', ')}`)
    
    if (dRingStaff.length > 0 && dRingStaff.length < allStaff.length) {
      console.log('✅ Staff filtering is working correctly')
      return true
    } else {
      console.log('❌ Staff filtering may not be working correctly')
      return false
    }
    
  } catch (error) {
    console.error('❌ Error testing staff filtering:', error)
    return false
  }
}

/**
 * Test 4: Check location access restrictions
 */
async function testLocationAccess() {
  console.log('\n🔒 Test 4: Testing location access restrictions...')
  
  try {
    // Get all locations
    const response = await fetch('/api/locations')
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    const allLocations = data.locations || []
    
    console.log(`   - Total locations in system: ${allLocations.length}`)
    
    // Find D-Ring Road location
    const dRingLocation = allLocations.find(loc => loc.id === MEKDES_CONFIG.expectedLocationId)
    if (dRingLocation) {
      console.log(`✅ D-Ring Road location found: ${dRingLocation.name}`)
    } else {
      console.log('❌ D-Ring Road location not found')
      return false
    }
    
    // Other locations that Mekdes should NOT have access to
    const otherLocations = allLocations.filter(loc => loc.id !== MEKDES_CONFIG.expectedLocationId)
    console.log(`   - Other locations Mekdes should NOT access: ${otherLocations.map(l => l.name).join(', ')}`)
    
    return true
  } catch (error) {
    console.error('❌ Error testing location access:', error)
    return false
  }
}

/**
 * Test 5: Verify calendar would show correct staff
 */
async function testCalendarStaffDisplay() {
  console.log('\n📅 Test 5: Testing calendar staff display...')
  
  try {
    // Simulate what the calendar should show for Mekdes
    const currentLocation = MEKDES_CONFIG.expectedLocationId
    
    // Get staff data
    const response = await fetch('/api/staff')
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    const allStaff = data.staff || []
    
    // Filter staff like the calendar would
    const availableStaff = allStaff.filter(staff => 
      staff.locations && staff.locations.includes(currentLocation)
    )
    
    console.log(`   - Calendar should show ${availableStaff.length} staff members`)
    console.log(`   - Staff names: ${availableStaff.map(s => s.name).join(', ')}`)
    
    // Check that staff from other locations are excluded
    const excludedStaff = allStaff.filter(staff => 
      !staff.locations || !staff.locations.includes(currentLocation)
    )
    
    console.log(`   - Calendar should EXCLUDE ${excludedStaff.length} staff members`)
    console.log(`   - Excluded staff: ${excludedStaff.map(s => s.name).join(', ')}`)
    
    if (availableStaff.length > 0 && availableStaff.length < allStaff.length) {
      console.log('✅ Calendar staff filtering would work correctly')
      return true
    } else {
      console.log('❌ Calendar staff filtering may not work correctly')
      return false
    }
    
  } catch (error) {
    console.error('❌ Error testing calendar staff display:', error)
    return false
  }
}

/**
 * Main test runner
 */
async function runAllTests() {
  console.log('🧪 Running Mekdes Location and Calendar Filtering Tests')
  console.log('=' .repeat(80))
  
  const results = {
    accountCheck: false,
    loginSimulation: false,
    staffFiltering: false,
    locationAccess: false,
    calendarDisplay: false
  }
  
  try {
    // Run all tests
    results.accountCheck = await testMekdesAccount()
    results.loginSimulation = await testMekdesLogin()
    results.staffFiltering = await testStaffFiltering()
    results.locationAccess = await testLocationAccess()
    results.calendarDisplay = await testCalendarStaffDisplay()
    
    // Calculate overall success
    const totalTests = Object.keys(results).length
    const passedTests = Object.values(results).filter(Boolean).length
    const successRate = (passedTests / totalTests) * 100
    
    // Final summary
    console.log('\n' + '=' .repeat(80))
    console.log('📊 TEST RESULTS SUMMARY')
    console.log('=' .repeat(80))
    
    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? '✅ PASS' : '❌ FAIL'
      const testName = test.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
      console.log(`   ${status} - ${testName}`)
    })
    
    console.log(`\n📈 Overall Success Rate: ${passedTests}/${totalTests} (${successRate.toFixed(1)}%)`)
    
    if (successRate >= 80) {
      console.log('\n🎉 TESTS PASSED! Mekdes location restrictions should work correctly.')
      console.log('\n📋 Expected Behavior:')
      console.log(`   1. Mekdes can only access D-Ring Road location`)
      console.log(`   2. Calendar shows only D-Ring Road staff members`)
      console.log(`   3. Location selector is restricted to D-Ring Road only`)
      console.log(`   4. Cannot switch to other locations`)
      
      return true
    } else {
      console.log('\n❌ TESTS FAILED! Location restrictions may not work properly.')
      return false
    }
    
  } catch (error) {
    console.error('💥 Test runner failed:', error)
    return false
  }
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.testMekdesLocation = runAllTests
  console.log('🔧 Run testMekdesLocation() in the browser console to execute all tests')
} else {
  module.exports = { runAllTests, MEKDES_CONFIG }
}
