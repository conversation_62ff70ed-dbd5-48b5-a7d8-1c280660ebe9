/**
 * Update <PERSON><PERSON><PERSON>'s Role Assignment
 * 
 * This script updates <PERSON><PERSON><PERSON> to use the D-Ring Road Staff role
 * and restricts her location access to D-Ring Road only
 */

// <PERSON><PERSON><PERSON>'s details from the API response
const MEKDES_ID = "cmc4uojw60108nzzkoltleywf"
const MEKDES_EMAIL = "<EMAIL>"
const DRING_LOCATION_ID = "cmc4ul8je0001nzzklf81qile"

// Function to update <PERSON><PERSON><PERSON>'s role
async function updateMekdesRole() {
  console.log('🔄 Updating <PERSON><PERSON><PERSON>\'s role assignment...')
  
  try {
    // Step 1: Update her role to "D-Ring Road Staff"
    const updateData = {
      name: "<PERSON><PERSON><PERSON>",
      email: MEKDES_EMAIL,
      phone: "+974 1234 5680",
      role: "D-Ring Road Staff", // New role
      locations: [DRING_LOCATION_ID], // Only D-Ring Road
      status: "Active",
      homeService: false,
      employeeNumber: "VH001",
      dateOfBirth: "31-12-84",
      qidNumber: "28901234567",
      passportNumber: "*********",
      qidValidity: "12-25-26",
      passportValidity: "06-30-27",
      medicalValidity: "03-15-26",
      profileImage: "",
      profileImageType: "",
      specialties: []
    }
    
    console.log('📤 Sending update request...')
    const response = await fetch(`/api/staff/${MEKDES_ID}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData)
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const result = await response.json()
    console.log('✅ Successfully updated Mekdes Abebe\'s role!')
    console.log('📋 Update details:')
    console.log(`   - Name: ${result.staff.name}`)
    console.log(`   - Email: ${result.staff.email}`)
    console.log(`   - Role: ${result.staff.role}`)
    console.log(`   - Locations: ${result.staff.locations.length} location(s)`)
    
    return result
    
  } catch (error) {
    console.error('❌ Error updating Mekdes Abebe\'s role:', error)
    throw error
  }
}

// Function to verify the update
async function verifyMekdesUpdate() {
  console.log('🔍 Verifying Mekdes Abebe\'s updated role...')
  
  try {
    const response = await fetch('/api/staff')
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    const mekdes = data.staff.find(staff => staff.id === MEKDES_ID)
    
    if (!mekdes) {
      console.error('❌ Mekdes Abebe not found in staff list')
      return false
    }
    
    console.log('✅ Verification results:')
    console.log(`   - Name: ${mekdes.name}`)
    console.log(`   - Email: ${mekdes.email}`)
    console.log(`   - Role: ${mekdes.role}`)
    console.log(`   - Locations: ${mekdes.locations.length} location(s)`)
    console.log(`   - Location IDs: ${mekdes.locations.join(', ')}`)
    
    // Check if she only has D-Ring Road access
    const hasOnlyDRingAccess = mekdes.locations.length === 1 && 
                               mekdes.locations[0] === DRING_LOCATION_ID
    
    if (hasOnlyDRingAccess) {
      console.log('🎉 SUCCESS: Mekdes Abebe now has access only to D-Ring Road!')
    } else {
      console.log('⚠️  WARNING: Mekdes Abebe still has access to multiple locations')
    }
    
    return hasOnlyDRingAccess
    
  } catch (error) {
    console.error('❌ Error verifying update:', error)
    return false
  }
}

// Function to get location names for display
async function getLocationNames() {
  try {
    const response = await fetch('/api/locations')
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    const locationMap = {}
    data.locations.forEach(loc => {
      locationMap[loc.id] = loc.name
    })
    
    return locationMap
  } catch (error) {
    console.error('❌ Error fetching locations:', error)
    return {}
  }
}

// Main function to run the complete update process
async function configureMekdesLocationAccess() {
  console.log('🚀 Starting Mekdes Abebe location access configuration...')
  console.log('')
  
  try {
    // Step 1: Update her role
    await updateMekdesRole()
    console.log('')
    
    // Step 2: Verify the update
    const success = await verifyMekdesUpdate()
    console.log('')
    
    if (success) {
      console.log('🎉 CONFIGURATION COMPLETE!')
      console.log('📋 Summary:')
      console.log('   ✅ Mekdes Abebe assigned to "D-Ring Road Staff" role')
      console.log('   ✅ Location access restricted to D-Ring Road only')
      console.log('   ✅ She can now only see data from D-Ring Road location')
      console.log('')
      console.log('🔐 Location-based access control is now active for Mekdes Abebe!')
    } else {
      console.log('❌ CONFIGURATION INCOMPLETE')
      console.log('Please check the logs above for details.')
    }
    
  } catch (error) {
    console.error('💥 Configuration failed:', error)
  }
}

// Export functions for manual use
if (typeof window !== 'undefined') {
  window.updateMekdesRole = updateMekdesRole
  window.verifyMekdesUpdate = verifyMekdesUpdate
  window.configureMekdesLocationAccess = configureMekdesLocationAccess
  
  console.log('🚀 Mekdes role update script loaded!')
  console.log('📝 Available functions:')
  console.log('   - updateMekdesRole() - Update her role assignment')
  console.log('   - verifyMekdesUpdate() - Verify the update worked')
  console.log('   - configureMekdesLocationAccess() - Run complete configuration')
  console.log('')
  console.log('💡 To update Mekdes\' role, run: configureMekdesLocationAccess()')
}

// Auto-run if this is being executed directly
if (typeof window !== 'undefined') {
  // We're in a browser environment, auto-run the configuration
  configureMekdesLocationAccess()
}
