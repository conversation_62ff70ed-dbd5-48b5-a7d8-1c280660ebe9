# Location-Based Access Control Configuration Guide

## Overview

This guide provides step-by-step instructions for configuring location-based access control for <PERSON><PERSON><PERSON> in the VanityERP system, restricting her access to only the D-Ring Road location.

## Prerequisites

- VanityERP system running on localhost:3200
- Admin access to the dashboard
- <PERSON><PERSON><PERSON> exists in the staff database

## Step 1: Create D-Ring Road Staff Role

### Option A: Using the Admin Dashboard (Recommended)

1. **Navigate to Settings**
   - Open browser to: `http://localhost:3200/dashboard/settings`
   - Click on the "User Management" tab
   - Go to the "Roles" section

2. **Create New Role**
   - Click "Add Role" button
   - Fill in the role details:
     - **Name**: `D-Ring Road Staff`
     - **Description**: `Staff members with access restricted to D-Ring Road location only`
     - **Permissions**: Select staff-level permissions:
       - ✅ View Appointments
       - ✅ Manage Appointments
       - ✅ View Clients
       - ✅ Manage Clients
       - ✅ View Services
       - ✅ View Inventory
       - ✅ Process Sales
       - ✅ View Reports

3. **Configure Location Access**
   - In the "Location Access" section:
     - Select "Specific Locations"
     - Choose only "📍 D-Ring Road"
     - Ensure no other locations are selected

4. **Save the Role**
   - Click "Add Role" to save

### Option B: Using Browser Console Script

1. **Open Browser Console**
   - Navigate to: `http://localhost:3200/dashboard/settings`
   - Press F12 to open Developer Tools
   - Go to Console tab

2. **Load and Run Script**
   ```javascript
   // Copy and paste the contents of scripts/create-dring-role.js
   // Or run directly:
   createDRingRoadStaffRole()
   ```

## Step 2: Update Mekdes Abebe's Role Assignment

### Option A: Using the Admin Dashboard

1. **Navigate to Staff Management**
   - Go to: `http://localhost:3200/dashboard/staff`
   - Find "Mekdes Abebe" in the staff list

2. **Edit Staff Member**
   - Click the "Edit" button (pencil icon) for Mekdes Abebe
   - In the edit dialog:
     - **Role**: Change to "D-Ring Road Staff"
     - **Locations**: Ensure only "D-Ring Road" is selected
     - Remove any other location assignments

3. **Save Changes**
   - Click "Save" to update her profile

### Option B: Using Browser Console Script

1. **Open Browser Console**
   - Navigate to: `http://localhost:3200/dashboard/staff`
   - Press F12 and go to Console tab

2. **Load and Run Script**
   ```javascript
   // Copy and paste the contents of scripts/update-mekdes-role.js
   // Or run directly:
   configureMekdesLocationAccess()
   ```

## Step 3: Verify Configuration

### Using Test Script

1. **Open Browser Console**
   - Navigate to: `http://localhost:3200/dashboard/settings`
   - Press F12 and go to Console tab

2. **Load and Run Test Script**
   ```javascript
   // Copy and paste the contents of scripts/test-location-access.js
   // Or run directly:
   runLocationAccessTests()
   ```

3. **Review Test Results**
   - All tests should pass
   - Verify that Mekdes can only access D-Ring Road data

### Manual Verification

1. **Check Role Configuration**
   - Go to Settings > User Management > Roles
   - Verify "D-Ring Road Staff" role exists
   - Confirm it has "Specific Locations" access to only "D-Ring Road"

2. **Check Staff Assignment**
   - Go to Staff Management
   - Find Mekdes Abebe
   - Verify her role is "D-Ring Road Staff"
   - Confirm she's only assigned to "D-Ring Road" location

## Step 4: Test Login and Access Control

### Create Test Login Credentials

1. **Ensure Mekdes has login credentials**
   - Email: `<EMAIL>`
   - Password: `temp123` (default for staff)

2. **Test Login Process**
   - Open incognito/private browser window
   - Navigate to: `http://localhost:3200/login`
   - Login with Mekdes' credentials

### Verify Location-Based Restrictions

When logged in as Mekdes Abebe, verify:

1. **Dashboard Data**
   - Only shows appointments from D-Ring Road
   - Client list filtered to D-Ring Road clients only
   - Inventory shows only D-Ring Road stock

2. **Location Selector**
   - Location dropdown should only show "D-Ring Road"
   - No access to other locations (Muaither, Medinat Khalifa, etc.)

3. **Reports and Analytics**
   - All reports filtered to D-Ring Road data only
   - Sales data shows only D-Ring Road transactions

## Expected Results

After successful configuration:

### ✅ What Mekdes CAN Access:
- Appointments scheduled at D-Ring Road location
- Clients associated with D-Ring Road
- Inventory and products at D-Ring Road
- Sales and transactions from D-Ring Road
- Reports filtered to D-Ring Road data only
- Services available at D-Ring Road

### ❌ What Mekdes CANNOT Access:
- Appointments from other locations (Muaither, Medinat Khalifa)
- Clients from other locations
- Inventory from other locations
- Sales data from other locations
- Cross-location reports or analytics
- Staff management for other locations

## Troubleshooting

### Issue: Role Not Created
- **Solution**: Check browser console for errors
- **Alternative**: Manually create role through admin dashboard

### Issue: Staff Update Failed
- **Solution**: Verify Mekdes exists in database
- **Check**: API endpoint `/api/staff` returns her data

### Issue: Location Access Not Working
- **Solution**: Clear browser cache and localStorage
- **Verify**: Role has correct `locationAccess` configuration

### Issue: Login Failed
- **Solution**: Reset password or check user account status
- **Alternative**: Use admin account to verify configuration

## API Endpoints Used

- `GET /api/staff` - Retrieve staff list
- `PUT /api/staff/{id}` - Update staff member
- `GET /api/locations` - Get location list
- `POST /api/configure-mekdes-access` - Automated configuration

## Files Created

- `scripts/create-dring-role.js` - Role creation script
- `scripts/update-mekdes-role.js` - Staff update script  
- `scripts/test-location-access.js` - Testing script
- `app/api/configure-mekdes-access/route.ts` - API endpoint

## Security Notes

- Location-based access control is enforced at the role level
- Individual user location assignments are secondary to role permissions
- All data filtering happens server-side for security
- Client-side scripts are for configuration and testing only

## Next Steps

1. Test the configuration thoroughly
2. Train Mekdes on the new access restrictions
3. Monitor system logs for any access issues
4. Consider implementing similar restrictions for other staff members
5. Document the process for future role configurations

---

**Configuration Complete!** 🎉

Mekdes Abebe now has location-based access control restricting her to D-Ring Road location data only.
