/**
 * Configure Location-Based Access Control for Mek<PERSON>
 * 
 * This script:
 * 1. Creates a new "D-Ring Road Staff" role with location restrictions
 * 2. Updates <PERSON><PERSON><PERSON>'s role assignment
 * 3. Verifies the configuration
 */

import { PrismaClient } from '@prisma/client'
import { SettingsStorage, Role } from '@/lib/settings-storage'

const prisma = new PrismaClient()

async function configureMekdesLocationAccess() {
  console.log('🔧 Configuring location-based access control for Mek<PERSON> Abebe...')

  try {
    // Step 1: Create D-Ring Road Staff Role
    console.log('\n📋 Step 1: Creating D-Ring Road Staff Role...')
    
    // Get existing roles
    const existingRoles = SettingsStorage.getRoles()
    console.log(`Found ${existingRoles.length} existing roles`)

    // Check if D-Ring Road Staff role already exists
    const existingDRingRole = existingRoles.find(role => 
      role.name === 'D-Ring Road Staff' || role.id === 'd-ring-road-staff'
    )

    let dRingRoadStaffRole: Role

    if (existingDRingRole) {
      console.log('✅ D-Ring Road Staff role already exists, updating it...')
      dRingRoadStaffRole = {
        ...existingDRingRole,
        name: 'D-Ring Road Staff',
        description: 'Staff members with access restricted to D-Ring Road location only',
        permissions: [
          'view_appointments',
          'manage_appointments',
          'view_clients',
          'manage_clients',
          'view_services',
          'view_inventory',
          'process_sales',
          'view_reports'
        ],
        locationAccess: {
          type: 'specific',
          locations: ['loc1'] // D-Ring Road location ID
        }
      }

      // Update existing role
      const updatedRoles = existingRoles.map(role =>
        role.id === existingDRingRole.id ? dRingRoadStaffRole : role
      )
      SettingsStorage.saveRoles(updatedRoles)
    } else {
      console.log('➕ Creating new D-Ring Road Staff role...')
      dRingRoadStaffRole = {
        id: 'd-ring-road-staff',
        name: 'D-Ring Road Staff',
        description: 'Staff members with access restricted to D-Ring Road location only',
        userCount: 0,
        permissions: [
          'view_appointments',
          'manage_appointments',
          'view_clients',
          'manage_clients',
          'view_services',
          'view_inventory',
          'process_sales',
          'view_reports'
        ],
        locationAccess: {
          type: 'specific',
          locations: ['loc1'] // D-Ring Road location ID
        }
      }

      // Add new role
      const updatedRoles = [...existingRoles, dRingRoadStaffRole]
      SettingsStorage.saveRoles(updatedRoles)
    }

    console.log('✅ D-Ring Road Staff role configured successfully')
    console.log(`   - Role ID: ${dRingRoadStaffRole.id}`)
    console.log(`   - Location Access: ${dRingRoadStaffRole.locationAccess?.type}`)
    console.log(`   - Allowed Locations: ${dRingRoadStaffRole.locationAccess?.locations.join(', ')}`)

    // Step 2: Find Mekdes Abebe in the database
    console.log('\n👤 Step 2: Finding Mekdes Abebe in the database...')
    
    const mekdesUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        staffMember: {
          include: {
            locations: {
              include: {
                location: true
              }
            }
          }
        }
      }
    })

    if (!mekdesUser) {
      console.log('❌ Mekdes Abebe not found in database')
      console.log('🔍 Searching for alternative email addresses...')
      
      // Try alternative email
      const mekdesAlt = await prisma.user.findUnique({
        where: { email: '<EMAIL>' },
        include: {
          staffMember: {
            include: {
              locations: {
                include: {
                  location: true
                }
              }
            }
          }
        }
      })

      if (!mekdesAlt) {
        console.log('❌ Mekdes Abebe not found with alternative email either')
        console.log('📋 Available staff members:')
        
        const allStaff = await prisma.staffMember.findMany({
          include: {
            user: true,
            locations: {
              include: {
                location: true
              }
            }
          }
        })

        allStaff.forEach(staff => {
          console.log(`   - ${staff.name} (${staff.user.email}) - Role: ${staff.user.role}`)
        })
        
        throw new Error('Mekdes Abebe not found in database')
      } else {
        console.log('✅ Found Mekdes Abebe with alternative email: <EMAIL>')
        await updateMekdesRole(mekdesAlt, dRingRoadStaffRole.id)
      }
    } else {
      console.log('✅ Found Mekdes Abebe in database')
      console.log(`   - Name: ${mekdesUser.staffMember?.name}`)
      console.log(`   - Email: ${mekdesUser.email}`)
      console.log(`   - Current Role: ${mekdesUser.role}`)
      console.log(`   - Current Locations: ${mekdesUser.staffMember?.locations.map(l => l.location.name).join(', ')}`)

      await updateMekdesRole(mekdesUser, dRingRoadStaffRole.id)
    }

    console.log('\n✅ Location-based access control configured successfully for Mekdes Abebe!')
    console.log('\n📋 Summary:')
    console.log('   - Created/Updated D-Ring Road Staff role')
    console.log('   - Assigned Mekdes Abebe to D-Ring Road Staff role')
    console.log('   - Restricted her access to D-Ring Road location only')
    console.log('\n🔐 Mekdes Abebe now has location-based access control:')
    console.log('   - Can only see appointments from D-Ring Road')
    console.log('   - Can only access clients from D-Ring Road')
    console.log('   - Can only view inventory from D-Ring Road')
    console.log('   - Can only process sales for D-Ring Road')
    console.log('   - Reports will be filtered to D-Ring Road only')

  } catch (error) {
    console.error('❌ Error configuring location-based access control:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

async function updateMekdesRole(user: any, newRoleId: string) {
  console.log('\n🔄 Step 3: Updating Mekdes Abebe\'s role assignment...')
  
  // Update user role in database to STAFF (since D-Ring Road Staff maps to STAFF)
  await prisma.user.update({
    where: { id: user.id },
    data: {
      role: 'STAFF' // Database role
    }
  })

  // Update staff member's job role to the new role name
  if (user.staffMember) {
    await prisma.staffMember.update({
      where: { id: user.staffMember.id },
      data: {
        jobRole: 'D-Ring Road Staff' // Specific job role for display
      }
    })
  }

  console.log('✅ Updated Mekdes Abebe\'s role assignment')
  console.log(`   - Database Role: STAFF`)
  console.log(`   - Job Role: D-Ring Road Staff`)
  console.log(`   - Location Access: D-Ring Road only`)
}

// Run the configuration
if (require.main === module) {
  configureMekdesLocationAccess()
    .then(() => {
      console.log('\n🎉 Configuration completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n💥 Configuration failed:', error)
      process.exit(1)
    })
}

export { configureMekdesLocationAccess }
