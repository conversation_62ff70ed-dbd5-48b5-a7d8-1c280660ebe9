/**
 * Test suite for location-based permissions functionality
 */

import { SettingsStorage, Role } from '@/lib/settings-storage'

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
global.localStorage = localStorageMock as any

// Mock SettingsStorage
jest.mock('@/lib/settings-storage', () => ({
  SettingsStorage: {
    getRoles: jest.fn(),
    saveRoles: jest.fn(),
  },
}))

describe('Location-Based Permissions', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Role Interface', () => {
    it('should support location access configuration', () => {
      const role: Role = {
        id: 'test-role',
        name: 'Test Role',
        description: 'Test role with location access',
        userCount: 0,
        permissions: ['view_appointments'],
        locationAccess: {
          type: 'specific',
          locations: ['loc1', 'loc2']
        }
      }

      expect(role.locationAccess).toBeDefined()
      expect(role.locationAccess?.type).toBe('specific')
      expect(role.locationAccess?.locations).toEqual(['loc1', 'loc2'])
    })

    it('should support all locations access', () => {
      const role: Role = {
        id: 'admin-role',
        name: 'Admin Role',
        description: 'Admin role with all location access',
        userCount: 0,
        permissions: ['all'],
        locationAccess: {
          type: 'all',
          locations: []
        }
      }

      expect(role.locationAccess?.type).toBe('all')
      expect(role.locationAccess?.locations).toEqual([])
    })
  })

  describe('Role Creation and Updates', () => {
    it('should create role with location access', () => {
      const mockRoles: Role[] = []
      ;(SettingsStorage.getRoles as jest.Mock).mockReturnValue(mockRoles)

      const newRole: Role = {
        id: 'location-manager',
        name: 'Location Manager',
        description: 'Manager for specific locations',
        userCount: 0,
        permissions: ['manage_appointments', 'view_clients'],
        locationAccess: {
          type: 'specific',
          locations: ['muaither', 'd-ring-road']
        }
      }

      const updatedRoles = [...mockRoles, newRole]
      SettingsStorage.saveRoles(updatedRoles)

      expect(SettingsStorage.saveRoles).toHaveBeenCalledWith(updatedRoles)
      expect(updatedRoles[0].locationAccess?.type).toBe('specific')
      expect(updatedRoles[0].locationAccess?.locations).toContain('muaither')
      expect(updatedRoles[0].locationAccess?.locations).toContain('d-ring-road')
    })

    it('should update existing role with location access', () => {
      const existingRole: Role = {
        id: 'staff',
        name: 'Staff',
        description: 'Regular staff member',
        userCount: 5,
        permissions: ['view_appointments'],
        locationAccess: {
          type: 'all',
          locations: []
        }
      }

      const mockRoles = [existingRole]
      ;(SettingsStorage.getRoles as jest.Mock).mockReturnValue(mockRoles)

      // Update to specific locations
      const updatedRole: Role = {
        ...existingRole,
        locationAccess: {
          type: 'specific',
          locations: ['medinat-khalifa']
        }
      }

      const updatedRoles = mockRoles.map(role => 
        role.id === updatedRole.id ? updatedRole : role
      )

      SettingsStorage.saveRoles(updatedRoles)

      expect(SettingsStorage.saveRoles).toHaveBeenCalledWith(updatedRoles)
      expect(updatedRoles[0].locationAccess?.type).toBe('specific')
      expect(updatedRoles[0].locationAccess?.locations).toEqual(['medinat-khalifa'])
    })
  })

  describe('Location Access Validation', () => {
    it('should validate specific location access', () => {
      const role: Role = {
        id: 'receptionist',
        name: 'Receptionist',
        description: 'Front desk staff',
        userCount: 2,
        permissions: ['view_appointments', 'manage_clients'],
        locationAccess: {
          type: 'specific',
          locations: ['muaither', 'd-ring-road']
        }
      }

      // Test location access validation
      const hasAccessToMuaither = role.locationAccess?.locations.includes('muaither')
      const hasAccessToDRing = role.locationAccess?.locations.includes('d-ring-road')
      const hasAccessToMedinat = role.locationAccess?.locations.includes('medinat-khalifa')

      expect(hasAccessToMuaither).toBe(true)
      expect(hasAccessToDRing).toBe(true)
      expect(hasAccessToMedinat).toBe(false)
    })

    it('should validate all locations access', () => {
      const role: Role = {
        id: 'manager',
        name: 'Manager',
        description: 'Location manager',
        userCount: 1,
        permissions: ['manage_staff', 'manage_appointments'],
        locationAccess: {
          type: 'all',
          locations: []
        }
      }

      // All locations access should allow access to any location
      expect(role.locationAccess?.type).toBe('all')
    })
  })

  describe('Default Role Configurations', () => {
    it('should have proper default location access for built-in roles', () => {
      const defaultRoles: Role[] = [
        {
          id: 'super_admin',
          name: 'Super Admin',
          description: 'Full access to all settings and features',
          userCount: 1,
          permissions: ['all'],
          locationAccess: {
            type: 'all',
            locations: []
          }
        },
        {
          id: 'location_manager',
          name: 'Location Manager',
          description: 'Manager for specific locations',
          userCount: 3,
          permissions: ['manage_staff', 'manage_appointments'],
          locationAccess: {
            type: 'specific',
            locations: []
          }
        },
        {
          id: 'staff',
          name: 'Staff',
          description: 'Regular staff member',
          userCount: 15,
          permissions: ['view_appointments', 'view_clients'],
          locationAccess: {
            type: 'specific',
            locations: []
          }
        }
      ]

      // Super admin should have all location access
      expect(defaultRoles[0].locationAccess?.type).toBe('all')
      
      // Location manager should have specific access (to be configured)
      expect(defaultRoles[1].locationAccess?.type).toBe('specific')
      
      // Staff should have specific access (to be configured)
      expect(defaultRoles[2].locationAccess?.type).toBe('specific')
    })
  })
})
