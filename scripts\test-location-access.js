/**
 * Test Location-Based Access Control for <PERSON><PERSON><PERSON>
 * 
 * This script tests that the location-based access control is working correctly
 * for <PERSON><PERSON><PERSON> after she's been assigned to the D-Ring Road Staff role
 */

// Test configuration
const MEKDES_EMAIL = "<EMAIL>"
const DRING_LOCATION_ID = "cmc4ul8je0001nzzklf81qile"
const DRING_LOCATION_NAME = "D-ring road"

// Function to simulate login as Mek<PERSON>
function simulateMekdesLogin() {
  console.log('🔐 Simulating login as <PERSON><PERSON><PERSON>...')
  
  // Create a mock user session for Mekdes
  const mekdesSession = {
    id: "cmc4uojw60108nzzkoltleywf",
    name: "<PERSON><PERSON><PERSON>",
    email: MEKDES_EMAIL,
    role: "D-Ring Road Staff",
    locations: [DRING_LOCATION_ID]
  }
  
  // Store in localStorage to simulate authentication
  localStorage.setItem('admin_user', JSON.stringify(mekdesSession))
  
  console.log('✅ Simulated login successful')
  console.log(`   - User: ${mekdesSession.name}`)
  console.log(`   - Role: ${mekdesSession.role}`)
  console.log(`   - Accessible Locations: ${mekdesSession.locations.length}`)
  
  return mekdesSession
}

// Function to test role-based location access
function testRoleBasedLocationAccess() {
  console.log('🧪 Testing role-based location access...')
  
  // Get the D-Ring Road Staff role from localStorage
  const roles = JSON.parse(localStorage.getItem('admin_roles') || '[]')
  const dRingRole = roles.find(role => role.name === 'D-Ring Road Staff')
  
  if (!dRingRole) {
    console.error('❌ D-Ring Road Staff role not found!')
    return false
  }
  
  console.log('✅ Found D-Ring Road Staff role:')
  console.log(`   - ID: ${dRingRole.id}`)
  console.log(`   - Name: ${dRingRole.name}`)
  console.log(`   - Location Access Type: ${dRingRole.locationAccess?.type}`)
  console.log(`   - Allowed Locations: ${dRingRole.locationAccess?.locations?.join(', ')}`)
  
  // Verify location access configuration
  const hasCorrectLocationAccess = 
    dRingRole.locationAccess?.type === 'specific' &&
    dRingRole.locationAccess?.locations?.length === 1 &&
    dRingRole.locationAccess?.locations?.includes('loc1')
  
  if (hasCorrectLocationAccess) {
    console.log('✅ Role has correct location access configuration')
    return true
  } else {
    console.error('❌ Role has incorrect location access configuration')
    return false
  }
}

// Function to test location filtering
function testLocationFiltering() {
  console.log('🔍 Testing location filtering...')
  
  // Get current user from localStorage
  const currentUser = JSON.parse(localStorage.getItem('admin_user') || '{}')
  
  if (!currentUser.email) {
    console.error('❌ No user logged in')
    return false
  }
  
  // Get roles to check location access
  const roles = JSON.parse(localStorage.getItem('admin_roles') || '[]')
  const userRole = roles.find(role => role.name === currentUser.role)
  
  if (!userRole) {
    console.error('❌ User role not found')
    return false
  }
  
  // Simulate location filtering logic
  let accessibleLocations = []
  
  if (userRole.locationAccess?.type === 'all') {
    accessibleLocations = ['all']
  } else if (userRole.locationAccess?.type === 'specific') {
    accessibleLocations = userRole.locationAccess.locations || []
  }
  
  console.log('📍 Location access test results:')
  console.log(`   - User: ${currentUser.name}`)
  console.log(`   - Role: ${currentUser.role}`)
  console.log(`   - Access Type: ${userRole.locationAccess?.type}`)
  console.log(`   - Accessible Locations: ${accessibleLocations.join(', ')}`)
  
  // Test specific location access
  const canAccessDRing = accessibleLocations.includes('loc1')
  const canAccessMuaither = accessibleLocations.includes('loc2')
  const canAccessMedinat = accessibleLocations.includes('loc3')
  
  console.log('🔐 Location access permissions:')
  console.log(`   - D-Ring Road (loc1): ${canAccessDRing ? '✅ ALLOWED' : '❌ DENIED'}`)
  console.log(`   - Muaither (loc2): ${canAccessMuaither ? '❌ SHOULD BE DENIED' : '✅ CORRECTLY DENIED'}`)
  console.log(`   - Medinat Khalifa (loc3): ${canAccessMedinat ? '❌ SHOULD BE DENIED' : '✅ CORRECTLY DENIED'}`)
  
  // Verify correct access control
  const hasCorrectAccess = canAccessDRing && !canAccessMuaither && !canAccessMedinat
  
  if (hasCorrectAccess) {
    console.log('🎉 Location filtering is working correctly!')
    return true
  } else {
    console.error('❌ Location filtering is not working correctly')
    return false
  }
}

// Function to test data filtering simulation
function testDataFiltering() {
  console.log('📊 Testing data filtering simulation...')
  
  // Simulate appointment data from different locations
  const mockAppointments = [
    { id: 1, clientName: "Sarah Ahmed", location: "cmc4ul8je0001nzzklf81qile", locationName: "D-ring road" },
    { id: 2, clientName: "Ahmed Ali", location: "cmc4ul8lg0002nzzkxwhu1tiz", locationName: "Muaither" },
    { id: 3, clientName: "Fatima Hassan", location: "cmc4ul8je0001nzzklf81qile", locationName: "D-ring road" },
    { id: 4, clientName: "Omar Khalil", location: "cmc4ul8nn0003nzzkffqvfnl4", locationName: "Medinat Khalifa" }
  ]
  
  // Get current user's accessible locations
  const currentUser = JSON.parse(localStorage.getItem('admin_user') || '{}')
  const roles = JSON.parse(localStorage.getItem('admin_roles') || '[]')
  const userRole = roles.find(role => role.name === currentUser.role)
  
  let accessibleLocationIds = []
  if (userRole?.locationAccess?.type === 'specific') {
    // Map role location IDs to actual database location IDs
    const locationMapping = {
      'loc1': 'cmc4ul8je0001nzzklf81qile', // D-Ring Road
      'loc2': 'cmc4ul8lg0002nzzkxwhu1tiz', // Muaither
      'loc3': 'cmc4ul8nn0003nzzkffqvfnl4'  // Medinat Khalifa
    }
    
    accessibleLocationIds = userRole.locationAccess.locations.map(
      locId => locationMapping[locId]
    ).filter(Boolean)
  }
  
  // Filter appointments based on location access
  const filteredAppointments = mockAppointments.filter(apt => 
    accessibleLocationIds.includes(apt.location)
  )
  
  console.log('📅 Appointment filtering test:')
  console.log(`   - Total appointments: ${mockAppointments.length}`)
  console.log(`   - Accessible to Mekdes: ${filteredAppointments.length}`)
  console.log('   - Visible appointments:')
  
  filteredAppointments.forEach(apt => {
    console.log(`     • ${apt.clientName} at ${apt.locationName}`)
  })
  
  // Verify only D-Ring Road appointments are visible
  const onlyDRingVisible = filteredAppointments.every(apt => 
    apt.location === "cmc4ul8je0001nzzklf81qile"
  )
  
  if (onlyDRingVisible && filteredAppointments.length === 2) {
    console.log('✅ Data filtering is working correctly!')
    return true
  } else {
    console.error('❌ Data filtering is not working correctly')
    return false
  }
}

// Main test function
function runLocationAccessTests() {
  console.log('🧪 Running Location-Based Access Control Tests for Mekdes Abebe')
  console.log('=' .repeat(70))
  console.log('')
  
  let allTestsPassed = true
  
  try {
    // Test 1: Simulate login
    console.log('TEST 1: User Authentication')
    console.log('-'.repeat(30))
    simulateMekdesLogin()
    console.log('')
    
    // Test 2: Role-based location access
    console.log('TEST 2: Role-Based Location Access')
    console.log('-'.repeat(30))
    const roleTest = testRoleBasedLocationAccess()
    allTestsPassed = allTestsPassed && roleTest
    console.log('')
    
    // Test 3: Location filtering
    console.log('TEST 3: Location Filtering')
    console.log('-'.repeat(30))
    const filterTest = testLocationFiltering()
    allTestsPassed = allTestsPassed && filterTest
    console.log('')
    
    // Test 4: Data filtering simulation
    console.log('TEST 4: Data Filtering Simulation')
    console.log('-'.repeat(30))
    const dataTest = testDataFiltering()
    allTestsPassed = allTestsPassed && dataTest
    console.log('')
    
    // Final results
    console.log('FINAL RESULTS')
    console.log('='.repeat(70))
    
    if (allTestsPassed) {
      console.log('🎉 ALL TESTS PASSED!')
      console.log('✅ Location-based access control is working correctly for Mekdes Abebe')
      console.log('')
      console.log('📋 Summary:')
      console.log('   ✅ Mekdes can only access D-Ring Road location data')
      console.log('   ✅ She cannot see data from other locations')
      console.log('   ✅ Role-based permissions are properly configured')
      console.log('   ✅ Data filtering will work as expected')
    } else {
      console.log('❌ SOME TESTS FAILED!')
      console.log('Please review the test results above and fix any issues.')
    }
    
  } catch (error) {
    console.error('💥 Test execution failed:', error)
    allTestsPassed = false
  }
  
  return allTestsPassed
}

// Export functions for manual use
if (typeof window !== 'undefined') {
  window.runLocationAccessTests = runLocationAccessTests
  window.simulateMekdesLogin = simulateMekdesLogin
  window.testRoleBasedLocationAccess = testRoleBasedLocationAccess
  window.testLocationFiltering = testLocationFiltering
  window.testDataFiltering = testDataFiltering
  
  console.log('🧪 Location access test script loaded!')
  console.log('📝 Available functions:')
  console.log('   - runLocationAccessTests() - Run all tests')
  console.log('   - simulateMekdesLogin() - Simulate login as Mekdes')
  console.log('   - testRoleBasedLocationAccess() - Test role configuration')
  console.log('   - testLocationFiltering() - Test location filtering')
  console.log('   - testDataFiltering() - Test data filtering')
  console.log('')
  console.log('💡 To run all tests, execute: runLocationAccessTests()')
}
