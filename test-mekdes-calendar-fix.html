<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mekdes Calendar Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .results {
            margin-top: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Mekdes Calendar Staff Filtering</h1>
        
        <div class="test-section info">
            <h3>📋 Test Overview</h3>
            <p>This test verifies that Mekdes (location-restricted staff) only sees D-Ring Road staff members in the calendar, not all staff from all locations.</p>
            
            <p><strong>Expected Results:</strong></p>
            <ul>
                <li>✅ Mekdes should only see staff assigned to D-Ring Road location</li>
                <li>✅ Calendar should filter out staff from other locations (Muaither, Medinat Khalifa, etc.)</li>
                <li>✅ Location selector should be restricted to D-Ring Road only</li>
                <li>❌ Should NOT see staff from other locations</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔧 Test Actions</h3>
            <button onclick="setupMekdesLogin()">1. Setup Mekdes Login</button>
            <button onclick="testStaffFiltering()">2. Test Staff Filtering</button>
            <button onclick="testLocationAccess()">3. Test Location Access</button>
            <button onclick="testCalendarDisplay()">4. Test Calendar Display</button>
            <button onclick="runAllTests()">🚀 Run All Tests</button>
        </div>

        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div id="results" class="results">Click "Run All Tests" to start testing...</div>
        </div>
    </div>

    <script>
        const MEKDES_CONFIG = {
            email: "<EMAIL>",
            expectedLocationId: "cmc4ul8je0001nzzklf81qile", // D-Ring Road
            expectedLocationName: "D-ring road"
        };

        function log(message) {
            const results = document.getElementById('results');
            results.textContent += new Date().toLocaleTimeString() + ' - ' + message + '\n';
            console.log(message);
        }

        function clearResults() {
            document.getElementById('results').textContent = '';
        }

        async function setupMekdesLogin() {
            log('🔐 Setting up Mekdes login...');
            
            try {
                // Simulate Mekdes login by setting localStorage
                const mekdesUser = {
                    id: "cmc4uojw60108nzzkoltleywf",
                    name: "Mekdes",
                    email: MEKDES_CONFIG.email,
                    role: "staff",
                    locations: [MEKDES_CONFIG.expectedLocationId] // Only D-Ring Road
                };
                
                localStorage.setItem("vanity_user", JSON.stringify(mekdesUser));
                localStorage.setItem("vanity_location", MEKDES_CONFIG.expectedLocationId);
                
                log('✅ Mekdes login setup complete');
                log(`   - User ID: ${mekdesUser.id}`);
                log(`   - Name: ${mekdesUser.name}`);
                log(`   - Role: ${mekdesUser.role}`);
                log(`   - Assigned Locations: ${mekdesUser.locations.join(', ')}`);
                log(`   - Current Location: ${MEKDES_CONFIG.expectedLocationId}`);
                
                return true;
            } catch (error) {
                log('❌ Error setting up Mekdes login: ' + error.message);
                return false;
            }
        }

        async function testStaffFiltering() {
            log('\n👥 Testing staff filtering...');
            
            try {
                // Get all staff from API
                const response = await fetch('/api/staff');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                const allStaff = data.staff || [];
                
                log(`   - Total staff in system: ${allStaff.length}`);
                
                // Filter staff for D-Ring Road location (what Mekdes should see)
                const dRingStaff = allStaff.filter(staff => 
                    staff.locations && staff.locations.includes(MEKDES_CONFIG.expectedLocationId)
                );
                
                log(`   - Staff assigned to D-Ring Road: ${dRingStaff.length}`);
                log(`   - D-Ring Road staff names: ${dRingStaff.map(s => s.name).join(', ')}`);
                
                // Staff from other locations (should be filtered out)
                const otherLocationStaff = allStaff.filter(staff => 
                    staff.locations && !staff.locations.includes(MEKDES_CONFIG.expectedLocationId)
                );
                
                log(`   - Staff from other locations: ${otherLocationStaff.length}`);
                log(`   - Other location staff: ${otherLocationStaff.map(s => s.name).join(', ')}`);
                
                // Check if Mekdes is in the D-Ring Road staff list
                const mekdesInList = dRingStaff.find(staff => staff.name === "Mekdes");
                if (mekdesInList) {
                    log('✅ Mekdes is correctly assigned to D-Ring Road staff');
                } else {
                    log('❌ Mekdes is NOT found in D-Ring Road staff list');
                }
                
                if (dRingStaff.length > 0 && dRingStaff.length < allStaff.length) {
                    log('✅ Staff filtering logic is working correctly');
                    return true;
                } else {
                    log('❌ Staff filtering may not be working correctly');
                    return false;
                }
                
            } catch (error) {
                log('❌ Error testing staff filtering: ' + error.message);
                return false;
            }
        }

        async function testLocationAccess() {
            log('\n🔒 Testing location access restrictions...');
            
            try {
                // Get all locations
                const response = await fetch('/api/locations');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                const allLocations = data.locations || [];
                
                log(`   - Total locations in system: ${allLocations.length}`);
                
                // Find D-Ring Road location
                const dRingLocation = allLocations.find(loc => loc.id === MEKDES_CONFIG.expectedLocationId);
                if (dRingLocation) {
                    log(`✅ D-Ring Road location found: ${dRingLocation.name}`);
                } else {
                    log('❌ D-Ring Road location not found');
                    return false;
                }
                
                // Other locations that Mekdes should NOT have access to
                const otherLocations = allLocations.filter(loc => loc.id !== MEKDES_CONFIG.expectedLocationId);
                log(`   - Other locations Mekdes should NOT access: ${otherLocations.map(l => l.name).join(', ')}`);
                
                return true;
            } catch (error) {
                log('❌ Error testing location access: ' + error.message);
                return false;
            }
        }

        async function testCalendarDisplay() {
            log('\n📅 Testing calendar staff display simulation...');
            
            try {
                // Simulate what the calendar should show for Mekdes
                const currentLocation = MEKDES_CONFIG.expectedLocationId;
                
                // Get staff data
                const response = await fetch('/api/staff');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                const allStaff = data.staff || [];
                
                // Filter staff like the calendar should (after our fix)
                const availableStaff = allStaff.filter(staff => 
                    staff.locations && staff.locations.includes(currentLocation)
                );
                
                log(`   - Calendar should show ${availableStaff.length} staff members`);
                log(`   - Staff names: ${availableStaff.map(s => s.name).join(', ')}`);
                
                // Check that staff from other locations are excluded
                const excludedStaff = allStaff.filter(staff => 
                    !staff.locations || !staff.locations.includes(currentLocation)
                );
                
                log(`   - Calendar should EXCLUDE ${excludedStaff.length} staff members`);
                log(`   - Excluded staff: ${excludedStaff.map(s => s.name).join(', ')}`);
                
                if (availableStaff.length > 0 && availableStaff.length < allStaff.length) {
                    log('✅ Calendar staff filtering would work correctly');
                    return true;
                } else {
                    log('❌ Calendar staff filtering may not work correctly');
                    return false;
                }
                
            } catch (error) {
                log('❌ Error testing calendar staff display: ' + error.message);
                return false;
            }
        }

        async function runAllTests() {
            clearResults();
            log('🧪 Starting Mekdes Calendar Staff Filtering Tests');
            log('=' .repeat(60));
            
            const results = {
                setup: false,
                staffFiltering: false,
                locationAccess: false,
                calendarDisplay: false
            };
            
            try {
                // Run all tests
                results.setup = await setupMekdesLogin();
                results.staffFiltering = await testStaffFiltering();
                results.locationAccess = await testLocationAccess();
                results.calendarDisplay = await testCalendarDisplay();
                
                // Calculate overall success
                const totalTests = Object.keys(results).length;
                const passedTests = Object.values(results).filter(Boolean).length;
                const successRate = (passedTests / totalTests) * 100;
                
                // Final summary
                log('\n' + '=' .repeat(60));
                log('📊 TEST RESULTS SUMMARY');
                log('=' .repeat(60));
                
                Object.entries(results).forEach(([test, passed]) => {
                    const status = passed ? '✅ PASS' : '❌ FAIL';
                    const testName = test.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
                    log(`   ${status} - ${testName}`);
                });
                
                log(`\n📈 Overall Success Rate: ${passedTests}/${totalTests} (${successRate.toFixed(1)}%)`);
                
                if (successRate >= 80) {
                    log('\n🎉 TESTS PASSED! Mekdes calendar filtering should work correctly.');
                    log('\n📋 Next Steps:');
                    log('   1. Navigate to /dashboard/appointments');
                    log('   2. <NAME_EMAIL>');
                    log('   3. Verify calendar shows only D-Ring Road staff');
                    log('   4. Verify location selector is restricted');
                } else {
                    log('\n❌ TESTS FAILED! Calendar filtering may not work properly.');
                }
                
            } catch (error) {
                log('💥 Test runner failed: ' + error.message);
            }
        }

        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            log('🔧 Test page loaded. Ready to test Mekdes calendar filtering.');
            log('Click "Run All Tests" to verify the fix is working.');
        });
    </script>
</body>
</html>
