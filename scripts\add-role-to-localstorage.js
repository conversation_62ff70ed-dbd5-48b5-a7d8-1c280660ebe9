/**
 * Add D-Ring Road Staff Role to localStorage
 * 
 * This script adds the D-Ring Road Staff role to localStorage
 * so it appears in the admin dashboard settings
 */

function addDRingRoadStaffRoleToLocalStorage() {
  console.log('📋 Adding D-Ring Road Staff role to localStorage...')
  
  try {
    // Get existing roles from localStorage
    const existingRoles = JSON.parse(localStorage.getItem('admin_roles') || '[]')
    console.log(`Found ${existingRoles.length} existing roles`)

    // Check if role already exists
    const existingRole = existingRoles.find(role => 
      role.id === 'd-ring-road-staff' || role.name === 'D-Ring Road Staff'
    )

    if (existingRole) {
      console.log('✅ D-Ring Road Staff role already exists in localStorage')
      return true
    }

    // Create the new role
    const newRole = {
      id: 'd-ring-road-staff',
      name: 'D-Ring Road Staff',
      description: 'Staff members with access restricted to D-Ring Road location only',
      userCount: 1, // Mekdes Abebe
      permissions: [
        'view_appointments',
        'manage_appointments',
        'view_clients',
        'manage_clients',
        'view_services',
        'view_inventory',
        'process_sales',
        'view_reports'
      ],
      locationAccess: {
        type: 'specific',
        locations: ['loc1'] // D-Ring Road location ID
      }
    }

    // Add to existing roles
    const updatedRoles = [...existingRoles, newRole]
    localStorage.setItem('admin_roles', JSON.stringify(updatedRoles))
    
    console.log('✅ Successfully added D-Ring Road Staff role to localStorage')
    console.log(`   - Role ID: ${newRole.id}`)
    console.log(`   - Role Name: ${newRole.name}`)
    console.log(`   - Location Access: ${newRole.locationAccess.type}`)
    console.log(`   - Allowed Locations: ${newRole.locationAccess.locations.join(', ')}`)
    
    return true
    
  } catch (error) {
    console.error('❌ Error adding role to localStorage:', error)
    return false
  }
}

// Auto-run the function
if (typeof window !== 'undefined') {
  addDRingRoadStaffRoleToLocalStorage()
  
  // Refresh the page to show the new role
  setTimeout(() => {
    console.log('🔄 Refreshing page to show new role...')
    window.location.reload()
  }, 1000)
}

// Export for manual use
if (typeof window !== 'undefined') {
  window.addDRingRoadStaffRoleToLocalStorage = addDRingRoadStaffRoleToLocalStorage
}
