<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Staff Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Test Staff Login Flow</h1>
    <p>This page simulates the staff login process to test location-based access control.</p>

    <div class="section info">
        <h3>Test Scenario</h3>
        <p>Staff member should be assigned to D-Ring Road (loc1) and the location selector should automatically set to loc1.</p>
    </div>

    <div class="section">
        <h3>Step 1: Clear All Data</h3>
        <button onclick="clearAllData()">Clear All Data</button>
        <div id="clearResults"></div>
    </div>

    <div class="section">
        <h3>Step 2: Simulate Staff Login</h3>
        <button onclick="simulateStaffLogin()">Simulate Staff Login</button>
        <div id="loginResults"></div>
    </div>

    <div class="section">
        <h3>Step 3: Check Final State</h3>
        <button onclick="checkFinalState()">Check Final State</button>
        <div id="finalResults"></div>
    </div>

    <div class="section">
        <h3>Step 4: Test Location Access</h3>
        <button onclick="testLocationAccess()">Test Location Access</button>
        <div id="accessResults"></div>
    </div>

    <script>
        function clearAllData() {
            const clearResults = document.getElementById('clearResults');
            
            try {
                localStorage.removeItem('vanity_user');
                localStorage.removeItem('vanity_location');
                localStorage.removeItem('admin_users');
                localStorage.removeItem('admin_roles');
                
                clearResults.innerHTML = '<p class="success">✅ Cleared all localStorage data</p>';
            } catch (error) {
                clearResults.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        function simulateStaffLogin() {
            const loginResults = document.getElementById('loginResults');
            
            try {
                // Step 1: Create the staff user data (simulating the login process)
                const staffUser = {
                    id: "1",
                    name: "Demo Staff",
                    email: "<EMAIL>",
                    role: "staff",
                    locations: ["loc1"] // D-Ring Road only
                };
                
                loginResults.innerHTML = '<p class="info">🔄 Simulating login process...</p>';
                
                // Step 2: Set user data (simulating the login function)
                localStorage.setItem('vanity_user', JSON.stringify(staffUser));
                
                // Step 3: Simulate the location-restricted user logic
                // For location-restricted users, immediately set their location
                if (!staffUser.locations.includes("all") && staffUser.locations.length > 0) {
                    const assignedLocation = staffUser.locations[0];
                    console.log("🔍 Login - Setting location-restricted user to:", assignedLocation);
                    localStorage.setItem('vanity_location', assignedLocation);
                    
                    loginResults.innerHTML = `
                        <p class="success">✅ Staff login simulated successfully</p>
                        <p><strong>User:</strong> ${staffUser.name}</p>
                        <p><strong>Role:</strong> ${staffUser.role}</p>
                        <p><strong>Assigned Locations:</strong> ${staffUser.locations.join(', ')}</p>
                        <p><strong>Current Location Set To:</strong> ${assignedLocation}</p>
                    `;
                } else {
                    loginResults.innerHTML = '<p class="error">❌ User is not location-restricted</p>';
                }
                
            } catch (error) {
                loginResults.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        function checkFinalState() {
            const finalResults = document.getElementById('finalResults');
            
            try {
                const storedUser = localStorage.getItem('vanity_user');
                const currentLocation = localStorage.getItem('vanity_location');
                
                if (!storedUser) {
                    finalResults.innerHTML = '<p class="error">❌ No user data found</p>';
                    return;
                }
                
                const user = JSON.parse(storedUser);
                
                let results = '<h4>Final State Check:</h4>';
                results += `<pre>User: ${JSON.stringify(user, null, 2)}</pre>`;
                results += `<p><strong>Current Location:</strong> ${currentLocation}</p>`;
                
                // Validate the state
                if (user.role === 'staff' && user.locations.includes('loc1') && currentLocation === 'loc1') {
                    results += '<p class="success">✅ CORRECT: Staff user properly assigned to loc1</p>';
                } else {
                    results += '<p class="error">❌ ISSUE: Staff user not properly configured</p>';
                    if (user.role !== 'staff') {
                        results += `<p class="error">- Role is ${user.role}, should be staff</p>`;
                    }
                    if (!user.locations.includes('loc1')) {
                        results += `<p class="error">- Locations are ${user.locations.join(', ')}, should include loc1</p>`;
                    }
                    if (currentLocation !== 'loc1') {
                        results += `<p class="error">- Current location is ${currentLocation}, should be loc1</p>`;
                    }
                }
                
                finalResults.innerHTML = results;
            } catch (error) {
                finalResults.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        function testLocationAccess() {
            const accessResults = document.getElementById('accessResults');
            
            try {
                const storedUser = localStorage.getItem('vanity_user');
                if (!storedUser) {
                    accessResults.innerHTML = '<p class="error">❌ No user data found</p>';
                    return;
                }
                
                const user = JSON.parse(storedUser);
                
                // Simulate the canAccessLocation function
                function canAccessLocation(locationId) {
                    if (!user) return false;

                    // Super admin and org admin can access all locations
                    if (user.role === 'super_admin' || user.role === 'org_admin') {
                        return true;
                    }

                    // Handle "all" location access - only for users with proper permissions
                    if (locationId === 'all') {
                        return user.role === 'super_admin' || 
                               user.role === 'org_admin' || 
                               user.locations.includes('all');
                    }

                    // Online location can be accessed by users who have it in their locations
                    if (locationId === 'online') {
                        return user.locations.includes('online') || user.locations.includes('all');
                    }

                    // If the user has "all" in their locations, they can access all locations
                    if (user.locations.includes('all')) {
                        return true;
                    }

                    // Check if the location is in the user's assigned locations
                    return user.locations.includes(locationId);
                }
                
                const testLocations = ['all', 'loc1', 'loc2', 'loc3', 'home', 'online'];
                
                let results = '<h4>Location Access Test Results:</h4><ul>';
                testLocations.forEach(locationId => {
                    const canAccess = canAccessLocation(locationId);
                    const status = canAccess ? '✅ CAN ACCESS' : '❌ CANNOT ACCESS';
                    const expected = locationId === 'loc1' ? '✅ EXPECTED' : '❌ EXPECTED';
                    results += `<li><strong>${locationId}</strong>: ${status} (${expected})</li>`;
                });
                results += '</ul>';
                
                // Test location selector logic
                const accessibleLocations = testLocations.filter(loc => canAccessLocation(loc));
                const shouldShowAll = canAccessLocation('all') && accessibleLocations.length > 1;
                
                results += `<h4>Location Selector Logic:</h4>`;
                results += `<p><strong>Should show "All Locations":</strong> ${shouldShowAll ? '❌ YES (WRONG)' : '✅ NO (CORRECT)'}</p>`;
                results += `<p><strong>Accessible locations:</strong> ${accessibleLocations.join(', ')}</p>`;
                
                accessResults.innerHTML = results;
            } catch (error) {
                accessResults.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
