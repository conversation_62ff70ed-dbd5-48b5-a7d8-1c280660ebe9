/**
 * Complete Mekdes <PERSON>be Location-Based Access Control Configuration
 * 
 * This script executes all necessary steps to configure location-based access control
 * for <PERSON><PERSON><PERSON>, restricting her access to D-Ring Road location only.
 */

// Configuration constants
const MEKDES_ID = "cmc4uojw60108nzzkoltleywf"
const MEKDES_EMAIL = "<EMAIL>"
const DRING_LOCATION_ID = "cmc4ul8je0001nzzklf81qile"
const ROLE_ID = "d-ring-road-staff"

// Step 1: Create D-Ring Road Staff Role
function createDRingRoadStaffRole() {
  console.log('📋 Step 1: Creating D-Ring Road Staff Role...')
  
  try {
    // Get existing roles from localStorage
    const existingRoles = JSON.parse(localStorage.getItem('admin_roles') || '[]')
    console.log(`Found ${existingRoles.length} existing roles`)

    // Check if role already exists
    const existingRole = existingRoles.find(role => 
      role.id === ROLE_ID || role.name === 'D-Ring Road Staff'
    )

    if (existingRole) {
      console.log('✅ D-Ring Road Staff role already exists, updating...')
      
      // Update existing role
      const updatedRole = {
        ...existingRole,
        name: 'D-Ring Road Staff',
        description: 'Staff members with access restricted to D-Ring Road location only',
        permissions: [
          'view_appointments',
          'manage_appointments',
          'view_clients',
          'manage_clients',
          'view_services',
          'view_inventory',
          'process_sales',
          'view_reports'
        ],
        locationAccess: {
          type: 'specific',
          locations: ['loc1'] // D-Ring Road location ID
        }
      }

      const updatedRoles = existingRoles.map(role =>
        role.id === existingRole.id ? updatedRole : role
      )
      localStorage.setItem('admin_roles', JSON.stringify(updatedRoles))
      
    } else {
      console.log('➕ Creating new D-Ring Road Staff role...')
      
      const newRole = {
        id: ROLE_ID,
        name: 'D-Ring Road Staff',
        description: 'Staff members with access restricted to D-Ring Road location only',
        userCount: 1, // Will have Mekdes
        permissions: [
          'view_appointments',
          'manage_appointments',
          'view_clients',
          'manage_clients',
          'view_services',
          'view_inventory',
          'process_sales',
          'view_reports'
        ],
        locationAccess: {
          type: 'specific',
          locations: ['loc1'] // D-Ring Road location ID
        }
      }

      const updatedRoles = [...existingRoles, newRole]
      localStorage.setItem('admin_roles', JSON.stringify(updatedRoles))
    }

    console.log('✅ D-Ring Road Staff role configured successfully')
    return true
    
  } catch (error) {
    console.error('❌ Error creating role:', error)
    return false
  }
}

// Step 2: Update Mekdes Abebe's Role Assignment
async function updateMekdesRoleAssignment() {
  console.log('👤 Step 2: Updating Mekdes Abebe\'s role assignment...')
  
  try {
    const updateData = {
      name: "Mekdes Abebe",
      email: MEKDES_EMAIL,
      phone: "+974 1234 5680",
      role: "D-Ring Road Staff", // New role
      locations: [DRING_LOCATION_ID], // Only D-Ring Road
      status: "Active",
      homeService: false,
      employeeNumber: "VH001",
      dateOfBirth: "31-12-84",
      qidNumber: "28901234567",
      passportNumber: "*********",
      qidValidity: "12-25-26",
      passportValidity: "06-30-27",
      medicalValidity: "03-15-26",
      profileImage: "",
      profileImageType: "",
      specialties: []
    }
    
    console.log('📤 Sending update request to API...')
    const response = await fetch(`/api/staff/${MEKDES_ID}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData)
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const result = await response.json()
    console.log('✅ Successfully updated Mekdes Abebe\'s role!')
    console.log(`   - Role: ${result.staff.role}`)
    console.log(`   - Locations: ${result.staff.locations.length} location(s)`)
    
    return true
    
  } catch (error) {
    console.error('❌ Error updating Mekdes role:', error)
    return false
  }
}

// Step 3: Verify Configuration
async function verifyConfiguration() {
  console.log('🔍 Step 3: Verifying configuration...')
  
  try {
    // Verify role exists
    const roles = JSON.parse(localStorage.getItem('admin_roles') || '[]')
    const dRingRole = roles.find(role => role.name === 'D-Ring Road Staff')
    
    if (!dRingRole) {
      console.error('❌ D-Ring Road Staff role not found')
      return false
    }
    
    console.log('✅ Role verification:')
    console.log(`   - Name: ${dRingRole.name}`)
    console.log(`   - Location Access: ${dRingRole.locationAccess?.type}`)
    console.log(`   - Allowed Locations: ${dRingRole.locationAccess?.locations?.join(', ')}`)
    
    // Verify staff assignment
    const response = await fetch('/api/staff')
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    const mekdes = data.staff.find(staff => staff.id === MEKDES_ID)
    
    if (!mekdes) {
      console.error('❌ Mekdes Abebe not found in staff list')
      return false
    }
    
    console.log('✅ Staff verification:')
    console.log(`   - Name: ${mekdes.name}`)
    console.log(`   - Role: ${mekdes.role}`)
    console.log(`   - Locations: ${mekdes.locations.length} location(s)`)
    
    // Check if she only has D-Ring Road access
    const hasOnlyDRingAccess = mekdes.locations.length === 1 && 
                               mekdes.locations[0] === DRING_LOCATION_ID
    
    if (hasOnlyDRingAccess) {
      console.log('🎉 SUCCESS: Mekdes has access only to D-Ring Road!')
      return true
    } else {
      console.log('⚠️  WARNING: Mekdes still has access to multiple locations')
      return false
    }
    
  } catch (error) {
    console.error('❌ Error verifying configuration:', error)
    return false
  }
}

// Step 4: Test Location-Based Access Control
function testLocationAccessControl() {
  console.log('🧪 Step 4: Testing location-based access control...')
  
  try {
    // Simulate Mekdes login
    const mekdesSession = {
      id: MEKDES_ID,
      name: "Mekdes Abebe",
      email: MEKDES_EMAIL,
      role: "D-Ring Road Staff",
      locations: [DRING_LOCATION_ID]
    }
    
    // Test role-based location access
    const roles = JSON.parse(localStorage.getItem('admin_roles') || '[]')
    const userRole = roles.find(role => role.name === mekdesSession.role)
    
    if (!userRole) {
      console.error('❌ User role not found')
      return false
    }
    
    // Check location access permissions
    let accessibleLocations = []
    if (userRole.locationAccess?.type === 'specific') {
      accessibleLocations = userRole.locationAccess.locations || []
    }
    
    const canAccessDRing = accessibleLocations.includes('loc1')
    const canAccessMuaither = accessibleLocations.includes('loc2')
    const canAccessMedinat = accessibleLocations.includes('loc3')
    
    console.log('🔐 Location access test results:')
    console.log(`   - D-Ring Road (loc1): ${canAccessDRing ? '✅ ALLOWED' : '❌ DENIED'}`)
    console.log(`   - Muaither (loc2): ${canAccessMuaither ? '❌ SHOULD BE DENIED' : '✅ CORRECTLY DENIED'}`)
    console.log(`   - Medinat Khalifa (loc3): ${canAccessMedinat ? '❌ SHOULD BE DENIED' : '✅ CORRECTLY DENIED'}`)
    
    const hasCorrectAccess = canAccessDRing && !canAccessMuaither && !canAccessMedinat
    
    if (hasCorrectAccess) {
      console.log('✅ Location access control is working correctly!')
      return true
    } else {
      console.error('❌ Location access control is not working correctly')
      return false
    }
    
  } catch (error) {
    console.error('❌ Error testing access control:', error)
    return false
  }
}

// Main execution function
async function completeConfiguration() {
  console.log('🚀 Starting Complete Mekdes Abebe Location-Based Access Control Configuration')
  console.log('='.repeat(80))
  console.log('')
  
  let allStepsSuccessful = true
  
  try {
    // Step 1: Create role
    const roleCreated = createDRingRoadStaffRole()
    allStepsSuccessful = allStepsSuccessful && roleCreated
    console.log('')
    
    // Step 2: Update staff assignment
    const staffUpdated = await updateMekdesRoleAssignment()
    allStepsSuccessful = allStepsSuccessful && staffUpdated
    console.log('')
    
    // Step 3: Verify configuration
    const configVerified = await verifyConfiguration()
    allStepsSuccessful = allStepsSuccessful && configVerified
    console.log('')
    
    // Step 4: Test access control
    const accessControlTested = testLocationAccessControl()
    allStepsSuccessful = allStepsSuccessful && accessControlTested
    console.log('')
    
    // Final results
    console.log('CONFIGURATION RESULTS')
    console.log('='.repeat(80))
    
    if (allStepsSuccessful) {
      console.log('🎉 CONFIGURATION COMPLETED SUCCESSFULLY!')
      console.log('')
      console.log('📋 Summary:')
      console.log('   ✅ D-Ring Road Staff role created/updated')
      console.log('   ✅ Mekdes Abebe assigned to D-Ring Road Staff role')
      console.log('   ✅ Location access restricted to D-Ring Road only')
      console.log('   ✅ Access control testing passed')
      console.log('')
      console.log('🔐 Mekdes Abebe now has location-based access control:')
      console.log('   • Can only see appointments from D-Ring Road')
      console.log('   • Can only access clients from D-Ring Road')
      console.log('   • Can only view inventory from D-Ring Road')
      console.log('   • Can only process sales for D-Ring Road')
      console.log('   • Reports will be filtered to D-Ring Road only')
      console.log('')
      console.log('🎯 Ready for production use!')
      
    } else {
      console.log('❌ CONFIGURATION INCOMPLETE')
      console.log('Some steps failed. Please review the logs above.')
    }
    
    return allStepsSuccessful
    
  } catch (error) {
    console.error('💥 Configuration failed:', error)
    return false
  }
}

// Export for manual use
if (typeof window !== 'undefined') {
  window.completeConfiguration = completeConfiguration
  window.createDRingRoadStaffRole = createDRingRoadStaffRole
  window.updateMekdesRoleAssignment = updateMekdesRoleAssignment
  window.verifyConfiguration = verifyConfiguration
  window.testLocationAccessControl = testLocationAccessControl
  
  console.log('🚀 Complete configuration script loaded!')
  console.log('💡 To run the complete configuration, execute: completeConfiguration()')
}

// Auto-run the configuration
if (typeof window !== 'undefined') {
  completeConfiguration()
}
