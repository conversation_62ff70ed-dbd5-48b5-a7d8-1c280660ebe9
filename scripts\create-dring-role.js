/**
 * Create D-Ring Road Staff Role
 * 
 * This script creates a new role with location-based access control
 * Run this in the browser console on the settings page
 */

// Function to create D-Ring Road Staff role
function createDRingRoadStaffRole() {
  console.log('🔧 Creating D-Ring Road Staff role...')
  
  // Get existing roles from localStorage
  const existingRoles = JSON.parse(localStorage.getItem('admin_roles') || '[]')
  console.log(`Found ${existingRoles.length} existing roles`)
  
  // Check if D-Ring Road Staff role already exists
  const existingDRingRole = existingRoles.find(role => 
    role.name === 'D-Ring Road Staff' || role.id === 'd-ring-road-staff'
  )
  
  if (existingDRingRole) {
    console.log('✅ D-Ring Road Staff role already exists, updating it...')
    
    // Update existing role
    const updatedRole = {
      ...existingDRingRole,
      name: 'D-Ring Road Staff',
      description: 'Staff members with access restricted to D-Ring Road location only',
      permissions: [
        'view_appointments',
        'manage_appointments',
        'view_clients',
        'manage_clients',
        'view_services',
        'view_inventory',
        'process_sales',
        'view_reports'
      ],
      locationAccess: {
        type: 'specific',
        locations: ['loc1'] // D-Ring Road location ID
      }
    }
    
    const updatedRoles = existingRoles.map(role =>
      role.id === existingDRingRole.id ? updatedRole : role
    )
    
    localStorage.setItem('admin_roles', JSON.stringify(updatedRoles))
    console.log('✅ Updated D-Ring Road Staff role')
    
  } else {
    console.log('➕ Creating new D-Ring Road Staff role...')
    
    const newRole = {
      id: 'd-ring-road-staff',
      name: 'D-Ring Road Staff',
      description: 'Staff members with access restricted to D-Ring Road location only',
      userCount: 0,
      permissions: [
        'view_appointments',
        'manage_appointments',
        'view_clients',
        'manage_clients',
        'view_services',
        'view_inventory',
        'process_sales',
        'view_reports'
      ],
      locationAccess: {
        type: 'specific',
        locations: ['loc1'] // D-Ring Road location ID
      }
    }
    
    const updatedRoles = [...existingRoles, newRole]
    localStorage.setItem('admin_roles', JSON.stringify(updatedRoles))
    console.log('✅ Created D-Ring Road Staff role')
  }
  
  console.log('🎉 D-Ring Road Staff role is ready!')
  console.log('📋 Role details:')
  console.log('   - Name: D-Ring Road Staff')
  console.log('   - Location Access: Specific (D-Ring Road only)')
  console.log('   - Permissions: Staff-level permissions')
  
  // Refresh the page to see the new role
  console.log('🔄 Refreshing page to show new role...')
  window.location.reload()
}

// Function to check current roles
function checkCurrentRoles() {
  const roles = JSON.parse(localStorage.getItem('admin_roles') || '[]')
  console.log('📋 Current roles:')
  roles.forEach(role => {
    console.log(`   - ${role.name} (${role.id})`)
    if (role.locationAccess) {
      console.log(`     Location Access: ${role.locationAccess.type}`)
      if (role.locationAccess.type === 'specific') {
        console.log(`     Locations: ${role.locationAccess.locations.join(', ')}`)
      }
    }
  })
  return roles
}

// Export functions for manual use
window.createDRingRoadStaffRole = createDRingRoadStaffRole
window.checkCurrentRoles = checkCurrentRoles

console.log('🚀 D-Ring Road Staff role creation script loaded!')
console.log('📝 Available functions:')
console.log('   - createDRingRoadStaffRole() - Create the new role')
console.log('   - checkCurrentRoles() - Check existing roles')
console.log('')
console.log('💡 To create the role, run: createDRingRoadStaffRole()')

// Auto-run if this is being executed directly
if (typeof window !== 'undefined') {
  // We're in a browser environment
  createDRingRoadStaffRole()
}
